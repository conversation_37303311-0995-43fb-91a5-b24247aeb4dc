// Test script for the comprehensive logging system
console.log('🔍 Testing Phantom Browser Logging System');
console.log('==========================================\n');

// Test 1: Check if Diagnostic<PERSON>og<PERSON> compiles correctly
function testDiagnosticLogger() {
    console.log('📊 Test 1: DiagnosticLogger Compilation');
    console.log('---------------------------------------');
    
    try {
        // Try to require the compiled DiagnosticLogger
        const fs = require('fs');
        const path = require('path');
        
        const loggerPath = path.join(__dirname, 'dist', 'utils', 'DiagnosticLogger.js');
        if (fs.existsSync(loggerPath)) {
            console.log('✅ DiagnosticLogger.js compiled successfully');
            
            // Check if the file contains expected methods
            const content = fs.readFileSync(loggerPath, 'utf8');
            const expectedMethods = ['logStartup', 'logNavigation', 'logWebview', 'logNetwork'];
            
            expectedMethods.forEach(method => {
                if (content.includes(method)) {
                    console.log(`✅ Method ${method} found`);
                } else {
                    console.log(`❌ Method ${method} missing`);
                }
            });
            
            return true;
        } else {
            console.log('❌ DiagnosticLogger.js not found');
            return false;
        }
    } catch (error) {
        console.log('❌ Error testing DiagnosticLogger:', error.message);
        return false;
    }
}

// Test 2: Check if LogViewer compiles correctly
function testLogViewer() {
    console.log('\n📋 Test 2: LogViewer Compilation');
    console.log('--------------------------------');
    
    try {
        const fs = require('fs');
        const path = require('path');
        
        const viewerPath = path.join(__dirname, 'dist', 'utils', 'LogViewer.js');
        if (fs.existsSync(viewerPath)) {
            console.log('✅ LogViewer.js compiled successfully');
            
            // Check if the file contains expected methods
            const content = fs.readFileSync(viewerPath, 'utf8');
            const expectedMethods = ['getLogFiles', 'analyzeLogFile', 'generateDiagnosticReport'];
            
            expectedMethods.forEach(method => {
                if (content.includes(method)) {
                    console.log(`✅ Method ${method} found`);
                } else {
                    console.log(`❌ Method ${method} missing`);
                }
            });
            
            return true;
        } else {
            console.log('❌ LogViewer.js not found');
            return false;
        }
    } catch (error) {
        console.log('❌ Error testing LogViewer:', error.message);
        return false;
    }
}

// Test 3: Check if main.js includes logging integration
function testMainIntegration() {
    console.log('\n🔧 Test 3: Main.js Logging Integration');
    console.log('-------------------------------------');
    
    try {
        const fs = require('fs');
        const path = require('path');
        
        const mainPath = path.join(__dirname, 'dist', 'main.js');
        if (fs.existsSync(mainPath)) {
            const content = fs.readFileSync(mainPath, 'utf8');
            
            const expectedIntegrations = [
                'DiagnosticLogger',
                'logNavigation',
                'logWebview',
                'webRequest.onBeforeRequest',
                'get-diagnostic-report'
            ];
            
            expectedIntegrations.forEach(integration => {
                if (content.includes(integration)) {
                    console.log(`✅ Integration ${integration} found`);
                } else {
                    console.log(`❌ Integration ${integration} missing`);
                }
            });
            
            return true;
        } else {
            console.log('❌ main.js not found');
            return false;
        }
    } catch (error) {
        console.log('❌ Error testing main.js integration:', error.message);
        return false;
    }
}

// Test 4: Check if renderer.js includes logging methods
function testRendererIntegration() {
    console.log('\n🖥️ Test 4: Renderer.js Logging Integration');
    console.log('------------------------------------------');
    
    try {
        const fs = require('fs');
        const path = require('path');
        
        const rendererPath = path.join(__dirname, 'renderer', 'renderer.js');
        if (fs.existsSync(rendererPath)) {
            const content = fs.readFileSync(rendererPath, 'utf8');
            
            const expectedMethods = [
                'logWebviewEvent',
                'logWebviewError',
                'logNavigation',
                'logSearchEngine',
                'showDiagnosticReport'
            ];
            
            expectedMethods.forEach(method => {
                if (content.includes(method)) {
                    console.log(`✅ Method ${method} found`);
                } else {
                    console.log(`❌ Method ${method} missing`);
                }
            });
            
            return true;
        } else {
            console.log('❌ renderer.js not found');
            return false;
        }
    } catch (error) {
        console.log('❌ Error testing renderer.js integration:', error.message);
        return false;
    }
}

// Test 5: Check executable distribution
function testExecutableDistribution() {
    console.log('\n📦 Test 5: Executable Distribution');
    console.log('----------------------------------');
    
    try {
        const fs = require('fs');
        const path = require('path');
        
        const executablePath = path.join(__dirname, 'executable-working', 'phantom-browser-working-win32-x64', 'resources', 'app');
        
        const filesToCheck = [
            'dist/utils/DiagnosticLogger.js',
            'dist/utils/LogViewer.js',
            'dist/main.js',
            'renderer/renderer.js'
        ];
        
        let allFilesPresent = true;
        
        filesToCheck.forEach(file => {
            const fullPath = path.join(executablePath, file);
            if (fs.existsSync(fullPath)) {
                console.log(`✅ ${file} present in executable`);
            } else {
                console.log(`❌ ${file} missing in executable`);
                allFilesPresent = false;
            }
        });
        
        return allFilesPresent;
    } catch (error) {
        console.log('❌ Error testing executable distribution:', error.message);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    const test1 = testDiagnosticLogger();
    const test2 = testLogViewer();
    const test3 = testMainIntegration();
    const test4 = testRendererIntegration();
    const test5 = testExecutableDistribution();
    
    console.log('\n🎯 Test Summary');
    console.log('===============');
    
    const results = [
        { name: 'DiagnosticLogger Compilation', passed: test1 },
        { name: 'LogViewer Compilation', passed: test2 },
        { name: 'Main.js Integration', passed: test3 },
        { name: 'Renderer.js Integration', passed: test4 },
        { name: 'Executable Distribution', passed: test5 }
    ];
    
    results.forEach(result => {
        console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
    });
    
    const passedCount = results.filter(r => r.passed).length;
    console.log(`\n📊 Results: ${passedCount}/${results.length} tests passed`);
    
    if (passedCount === results.length) {
        console.log('\n🎉 All logging system tests passed!');
        console.log('The comprehensive logging system is ready for use.');
        console.log('\n📋 Next Steps:');
        console.log('1. Launch the browser to test logging in action');
        console.log('2. Try navigating to websites to generate logs');
        console.log('3. Check the diagnostic report for browsing issues');
        return true;
    } else {
        console.log('\n⚠️ Some tests failed. Please review the issues above.');
        return false;
    }
}

// Execute tests
runAllTests().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
});
