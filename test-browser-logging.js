// Test script to verify browser logging system is working
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 Testing Phantom Browser Logging System');
console.log('==========================================\n');

// Function to check if browser process is running
function checkBrowserProcess() {
    return new Promise((resolve) => {
        const tasklist = spawn('tasklist', ['/FI', 'IMAGENAME eq phantom-browser-working.exe']);
        let output = '';
        
        tasklist.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        tasklist.on('close', (code) => {
            const isRunning = output.includes('phantom-browser-working.exe');
            resolve(isRunning);
        });
    });
}

// Function to check log files
function checkLogFiles() {
    console.log('📁 Checking for log files...');
    
    // Check if logs directory exists in user data
    const userDataPath = path.join(process.env.APPDATA, 'Phantom Browser', 'logs');
    console.log('Expected log directory:', userDataPath);
    
    if (fs.existsSync(userDataPath)) {
        console.log('✅ Log directory exists');
        
        const logFiles = fs.readdirSync(userDataPath).filter(file => file.endsWith('.log'));
        console.log(`✅ Found ${logFiles.length} log files`);
        
        if (logFiles.length > 0) {
            const latestLog = logFiles.sort().pop();
            const logPath = path.join(userDataPath, latestLog);
            const logContent = fs.readFileSync(logPath, 'utf8');
            
            console.log(`📄 Latest log file: ${latestLog}`);
            console.log(`📊 Log file size: ${logContent.length} characters`);
            
            // Check for expected log categories
            const categories = ['STARTUP', 'SEARCH', 'NAVIGATION', 'WEBVIEW', 'NETWORK'];
            categories.forEach(category => {
                const count = (logContent.match(new RegExp(`\\[${category}\\]`, 'g')) || []).length;
                console.log(`   ${category}: ${count} entries`);
            });
            
            // Show recent log entries
            const lines = logContent.split('\n').filter(line => line.trim());
            const recentLines = lines.slice(-10);
            
            console.log('\n📝 Recent log entries:');
            recentLines.forEach(line => {
                if (line.trim()) {
                    console.log(`   ${line}`);
                }
            });
            
            return true;
        } else {
            console.log('❌ No log files found');
            return false;
        }
    } else {
        console.log('❌ Log directory does not exist');
        return false;
    }
}

// Function to test browser functionality
async function testBrowserFunctionality() {
    console.log('\n🧪 Testing Browser Functionality');
    console.log('--------------------------------');
    
    const isRunning = await checkBrowserProcess();
    
    if (isRunning) {
        console.log('✅ Browser process is running');
        
        // Wait a moment for initialization
        console.log('⏳ Waiting for browser initialization...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Check log files
        const hasLogs = checkLogFiles();
        
        if (hasLogs) {
            console.log('\n🎉 Logging system is working!');
            console.log('\n📋 Next Steps:');
            console.log('1. Open Developer Tools (F12) in the browser');
            console.log('2. Check console for real-time logging with emojis');
            console.log('3. Try navigating to test websites');
            console.log('4. Monitor for error patterns');
            
            return true;
        } else {
            console.log('\n⚠️ Browser is running but logging may not be working');
            return false;
        }
    } else {
        console.log('❌ Browser process is not running');
        console.log('💡 Try launching the browser manually:');
        console.log('   executable-working/phantom-browser-working-win32-x64/phantom-browser-working.exe');
        return false;
    }
}

// Function to provide diagnostic instructions
function provideDiagnosticInstructions() {
    console.log('\n🔧 Manual Diagnostic Instructions');
    console.log('=================================');
    console.log('1. Launch browser: executable-working/phantom-browser-working-win32-x64/phantom-browser-working.exe');
    console.log('2. Open Developer Tools: Press F12');
    console.log('3. Check console for startup logs like:');
    console.log('   🚀 Initializing Phantom Browser...');
    console.log('   🔍 SearchEngine: Browser initialization started');
    console.log('   📺 Webview Event Log: {event: "did-start-loading"}');
    console.log('');
    console.log('4. Test navigation:');
    console.log('   - Type "example.com" in address bar');
    console.log('   - Type "test search" in address bar');
    console.log('   - Watch for navigation logs');
    console.log('');
    console.log('5. Generate diagnostic report:');
    console.log('   window.phantomBrowser.showDiagnosticReport()');
    console.log('');
    console.log('6. Check log files at:');
    console.log(`   ${path.join(process.env.APPDATA, 'Phantom Browser', 'logs')}`);
}

// Main execution
async function main() {
    try {
        const success = await testBrowserFunctionality();
        
        if (!success) {
            provideDiagnosticInstructions();
        }
        
        console.log('\n📊 Test Summary');
        console.log('===============');
        console.log(`Browser Status: ${success ? 'Running with Logging' : 'Needs Manual Check'}`);
        console.log('Logging System: Deployed and Ready');
        console.log('Next Action: Manual browser testing with Developer Tools');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        provideDiagnosticInstructions();
    }
}

main();
