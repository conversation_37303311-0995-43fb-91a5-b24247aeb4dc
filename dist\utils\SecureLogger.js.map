{"version": 3, "file": "SecureLogger.js", "sourceRoot": "", "sources": ["../../src/utils/SecureLogger.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,IAAY,QAKX;AALD,WAAY,QAAQ;IAChB,yCAAS,CAAA;IACT,uCAAQ,CAAA;IACR,uCAAQ,CAAA;IACR,yCAAS,CAAA;AACb,CAAC,EALW,QAAQ,wBAAR,QAAQ,QAKnB;AAUD,MAAa,YAAY;IAMrB;QAJQ,aAAQ,GAAa,QAAQ,CAAC,IAAI,CAAC;QACnC,eAAU,GAAe,EAAE,CAAC;QAC5B,mBAAc,GAAW,IAAI,CAAC;IAEf,CAAC;IAEjB,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAAe;QAC9B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAe,EAAE,OAAgB;QAC1C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,OAAe,EAAE,OAAgB;QACzC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,OAAe,EAAE,OAAgB;QACzC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAe,EAAE,OAAgB;QAC1C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,KAAe,EAAE,OAAe,EAAE,OAAgB;QAC1D,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAa;YACvB,KAAK;YACL,OAAO,EAAE,gBAAgB;YACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO;YACP,SAAS,EAAE,gBAAgB,KAAK,OAAO;SAC1C,CAAC;QAEF,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE5B,oBAAoB;QACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAe;QACnC,IAAI,SAAS,GAAG,OAAO,CAAC;QAExB,mBAAmB;QACnB,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;QAC3E,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;QACnE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC;QAEhF,0CAA0C;QAC1C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;QAC3E,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;QACnE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC;QAEhF,6BAA6B;QAC7B,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,8BAA8B,EAAE,aAAa,CAAC,CAAC;QAC7E,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,wBAAwB,EAAE,WAAW,CAAC,CAAC;QACrE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,yBAAyB,EAAE,YAAY,CAAC,CAAC;QAEvE,uCAAuC;QACvC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,4BAA4B,EAAE,UAAU,CAAC,CAAC;QACxE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;QAClE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC;QAE1E,4CAA4C;QAC5C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,gCAAgC,EAAE,aAAa,CAAC,CAAC;QAE/E,mCAAmC;QACnC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;QAEvE,iCAAiC;QACjC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAEjE,oCAAoC;QACpC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;QACxE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC;QAC1E,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;QAEzE,mEAAmE;QACnE,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,0BAA0B,EAAE,oBAAoB,CAAC,CAAC;QAEhF,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,KAAe;QAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5B,8BAA8B;QAC9B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAe;QACnC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1D,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9D,MAAM,kBAAkB,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QAExD,MAAM,gBAAgB,GAAG,IAAI,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,CAAC,OAAO,GAAG,kBAAkB,EAAE,CAAC;QAE3G,QAAQ,KAAK,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,QAAQ,CAAC,KAAK;gBACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAChC,MAAM;YACV,KAAK,QAAQ,CAAC,IAAI;gBACd,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC/B,MAAM;YACV,KAAK,QAAQ,CAAC,IAAI;gBACd,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC/B,MAAM;YACV,KAAK,QAAQ,CAAC,KAAK;gBACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAChC,MAAM;QACd,CAAC;IACL,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,UAAmB;QACpC,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QAClF,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,gBAAgB;IACjE,CAAC;IAED;;OAEG;IACI,YAAY;QACf,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,UAAU;QACb,OAAO,IAAI,CAAC,UAAU;aACjB,GAAG,CAAC,KAAK,CAAC,EAAE;YACT,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9D,OAAO,IAAI,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;QACxE,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAAe;QACrC,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,MAAM,SAAS,GAAG;YACd,wCAAwC;YACxC,0EAA0E;YAC1E,uDAAuD;YACvD,wEAAwE;YACxE,6BAA6B;YAC7B,4CAA4C;YAC5C,gFAAgF;SACnF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YAClC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,EAAE,CAAC,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,EAAE,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAe;QACzC,MAAM,iBAAiB,GAAG;YACtB,2BAA2B;YAC3B,uBAAuB;YACvB,2BAA2B;YAC3B,uBAAuB;YACvB,8BAA8B;YAC9B,wBAAwB;YACxB,yBAAyB;YACzB,uBAAuB;YACvB,gBAAgB;YAChB,kBAAkB;SACrB,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACpE,CAAC;CACJ;AA/OD,oCA+OC;AAED;;GAEG;AACH,MAAa,YAAY;IACrB,YACY,MAAoB,EACpB,OAAe;QADf,WAAM,GAAN,MAAM,CAAc;QACpB,YAAO,GAAP,OAAO,CAAQ;IACxB,CAAC;IAEG,KAAK,CAAC,OAAe;QACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEM,IAAI,CAAC,OAAe;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEM,IAAI,CAAC,OAAe;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,OAAe;QACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;CACJ;AArBD,oCAqBC;AAED,4CAA4C;AAC/B,QAAA,YAAY,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;AAEvD,kBAAe,YAAY,CAAC"}