import { ProxyManager, ProxyConfig } from './ProxyManager';
import { ApiResponse } from '../utils/ErrorHandler';
export interface NetworkConfiguration {
    priority: 'proxy' | 'doh' | 'direct';
    proxyConfig?: ProxyConfig;
    dohConfig?: {
        enabled: boolean;
        providers: string[];
        fallbackToDirect: boolean;
    };
    status: 'active' | 'inactive' | 'error';
    lastUpdated: number;
}
export interface DoHProvider {
    name: string;
    url: string;
    description: string;
    privacyRating: number;
}
export declare class NetworkConfigManager {
    private currentConfig;
    private proxyManager;
    private errorHandler;
    private settingsStore;
    private dohProviders;
    constructor(proxyManager: ProxyManager);
    private loadConfiguration;
    private saveConfiguration;
    setNetworkConfiguration(config: Partial<NetworkConfiguration>): Promise<ApiResponse<void>>;
    private validateConfiguration;
    private applyConfiguration;
    private clearAllNetworkConfiguration;
    private setupDoH;
    private generateDoHPacScript;
    getCurrentConfiguration(): NetworkConfiguration;
    getDoHProviders(): DoHProvider[];
    switchToProxy(proxyConfig: ProxyConfig): Promise<ApiResponse<void>>;
    switchToDoH(providers?: string[]): Promise<ApiResponse<void>>;
    switchToDirect(): Promise<ApiResponse<void>>;
    getNetworkStatus(): {
        mode: string;
        active: boolean;
        details: string;
        lastUpdated: Date;
    };
    destroy(): void;
}
export default NetworkConfigManager;
//# sourceMappingURL=NetworkConfigManager.d.ts.map