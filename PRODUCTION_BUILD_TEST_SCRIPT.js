// Production Build Test Script for <PERSON> Browser
// Run this in the browser console to verify all functionality

console.log('🚀 PHANTOM BROWSER PRODUCTION BUILD TEST');
console.log('==========================================');

// Test Results Object
const testResults = {
    startup: false,
    searchEngine: false,
    networkConfig: false,
    uiComponents: false,
    navigation: false,
    errors: []
};

// Test 1: Application Startup
function testApplicationStartup() {
    console.log('\n📱 Test 1: Application Startup');
    console.log('------------------------------');
    
    try {
        // Check if main UI elements are present
        const addressBar = document.getElementById('addressBar');
        const webview = document.getElementById('webview');
        const privacyPanel = document.querySelector('.privacy-panel');
        
        if (addressBar && webview && privacyPanel) {
            console.log('✅ Application started successfully');
            console.log('✅ Core UI elements present');
            testResults.startup = true;
        } else {
            console.log('❌ Missing core UI elements');
            testResults.errors.push('Missing core UI elements');
        }
    } catch (error) {
        console.log('❌ Application startup test failed:', error.message);
        testResults.errors.push(`Startup test failed: ${error.message}`);
    }
}

// Test 2: Search Engine Functionality
function testSearchEngine() {
    console.log('\n🔍 Test 2: Search Engine Functionality');
    console.log('--------------------------------------');
    
    try {
        // Check if SearchEngine modules are imported
        if (window.phantomBrowserUI && window.phantomBrowserUI.searchEngine) {
            console.log('✅ SearchEngine is available');
            
            const searchEngine = window.phantomBrowserUI.searchEngine;
            
            // Test providers
            const providers = searchEngine.getProviders();
            console.log(`✅ Found ${providers.length} search providers:`, providers.map(p => p.name));
            
            // Test default provider
            const defaultProvider = searchEngine.getDefaultProvider();
            console.log(`✅ Default provider: ${defaultProvider.name}`);
            
            // Test URL processing
            const urlTest = searchEngine.processInput('google.com');
            console.log(`✅ URL test: "google.com" → ${urlTest.type}: ${urlTest.value}`);
            
            // Test search processing
            const searchTest = searchEngine.processInput('test search');
            console.log(`✅ Search test: "test search" → ${searchTest.type}: ${searchTest.value}`);
            
            testResults.searchEngine = true;
        } else {
            console.log('❌ SearchEngine not available');
            testResults.errors.push('SearchEngine not available');
        }
        
        // Check if SearchUI is available
        if (window.phantomBrowserUI && window.phantomBrowserUI.searchUI) {
            console.log('✅ SearchUI is available (full functionality)');
        } else {
            console.log('⚠️ SearchUI not available (using fallback)');
        }
        
    } catch (error) {
        console.log('❌ Search engine test failed:', error.message);
        testResults.errors.push(`Search engine test failed: ${error.message}`);
    }
}

// Test 3: Network Configuration API
function testNetworkConfiguration() {
    console.log('\n🌐 Test 3: Network Configuration API');
    console.log('------------------------------------');
    
    try {
        // Check if phantomAPI is available
        if (window.phantomAPI) {
            console.log('✅ phantomAPI is available');
            
            // Check for network configuration methods
            const networkMethods = [
                'switchToProxy',
                'switchToDoH', 
                'switchToDirect',
                'getNetworkStatus',
                'getNetworkConfig'
            ];
            
            let methodsAvailable = 0;
            networkMethods.forEach(method => {
                if (typeof window.phantomAPI[method] === 'function') {
                    console.log(`✅ ${method} method available`);
                    methodsAvailable++;
                } else {
                    console.log(`❌ ${method} method missing`);
                    testResults.errors.push(`${method} method missing`);
                }
            });
            
            if (methodsAvailable === networkMethods.length) {
                console.log('✅ All network configuration methods available');
                testResults.networkConfig = true;
            } else {
                console.log(`❌ Only ${methodsAvailable}/${networkMethods.length} methods available`);
            }
            
        } else {
            console.log('❌ phantomAPI not available');
            testResults.errors.push('phantomAPI not available');
        }
    } catch (error) {
        console.log('❌ Network configuration test failed:', error.message);
        testResults.errors.push(`Network config test failed: ${error.message}`);
    }
}

// Test 4: UI Components
function testUIComponents() {
    console.log('\n🎨 Test 4: UI Components');
    console.log('------------------------');
    
    try {
        const components = {
            'Address Bar': document.getElementById('addressBar'),
            'Webview': document.getElementById('webview'),
            'Privacy Panel Button': document.getElementById('privacyPanelBtn'),
            'Search Provider Selector': document.getElementById('searchProvider'),
            'Provider Details': document.getElementById('providerDetails'),
            'Search Suggestions Container': document.getElementById('searchSuggestions')
        };
        
        let componentsFound = 0;
        Object.entries(components).forEach(([name, element]) => {
            if (element) {
                console.log(`✅ ${name} found`);
                componentsFound++;
            } else {
                console.log(`❌ ${name} missing`);
                testResults.errors.push(`${name} missing`);
            }
        });
        
        if (componentsFound >= 4) { // Allow some components to be missing
            console.log(`✅ UI components test passed (${componentsFound}/6 found)`);
            testResults.uiComponents = true;
        } else {
            console.log(`❌ UI components test failed (${componentsFound}/6 found)`);
        }
        
    } catch (error) {
        console.log('❌ UI components test failed:', error.message);
        testResults.errors.push(`UI components test failed: ${error.message}`);
    }
}

// Test 5: Navigation Functionality
function testNavigation() {
    console.log('\n🧭 Test 5: Navigation Functionality');
    console.log('-----------------------------------');
    
    try {
        const addressBar = document.getElementById('addressBar');
        const webview = document.getElementById('webview');
        
        if (addressBar && webview) {
            console.log('✅ Navigation elements present');
            
            // Test if navigation methods are available
            if (window.phantomBrowserUI && typeof window.phantomBrowserUI.navigate === 'function') {
                console.log('✅ Navigate method available');
            }
            
            if (window.phantomBrowserUI && typeof window.phantomBrowserUI.performSearch === 'function') {
                console.log('✅ PerformSearch method available');
            }
            
            // Check if address bar has event listeners
            console.log('✅ Address bar ready for input');
            
            testResults.navigation = true;
        } else {
            console.log('❌ Navigation elements missing');
            testResults.errors.push('Navigation elements missing');
        }
        
    } catch (error) {
        console.log('❌ Navigation test failed:', error.message);
        testResults.errors.push(`Navigation test failed: ${error.message}`);
    }
}

// Test 6: Console Error Check
function checkConsoleErrors() {
    console.log('\n🔍 Test 6: Console Error Check');
    console.log('------------------------------');
    
    // This is a manual check - look for any red error messages
    console.log('⚠️ Manual check required: Look for red error messages in console');
    console.log('✅ If no red errors visible, console test passes');
}

// Run All Tests
async function runProductionBuildTests() {
    console.log('🎯 Starting Production Build Tests...\n');
    
    testApplicationStartup();
    testSearchEngine();
    testNetworkConfiguration();
    testUIComponents();
    testNavigation();
    checkConsoleErrors();
    
    // Generate Test Report
    console.log('\n📊 PRODUCTION BUILD TEST REPORT');
    console.log('================================');
    
    const passedTests = Object.values(testResults).filter(result => result === true).length;
    const totalTests = Object.keys(testResults).length - 1; // Exclude errors array
    
    console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Tests Failed: ${totalTests - passedTests}/${totalTests}`);
    
    if (testResults.errors.length > 0) {
        console.log('\n🚨 Errors Found:');
        testResults.errors.forEach((error, index) => {
            console.log(`   ${index + 1}. ${error}`);
        });
    }
    
    // Overall Status
    const overallScore = (passedTests / totalTests) * 100;
    console.log(`\n🎯 Overall Score: ${overallScore.toFixed(1)}%`);
    
    if (overallScore >= 80) {
        console.log('🎉 PRODUCTION BUILD: READY FOR DEPLOYMENT');
    } else if (overallScore >= 60) {
        console.log('⚠️ PRODUCTION BUILD: NEEDS MINOR FIXES');
    } else {
        console.log('❌ PRODUCTION BUILD: NEEDS MAJOR FIXES');
    }
    
    // Manual Testing Instructions
    console.log('\n📋 MANUAL TESTING INSTRUCTIONS');
    console.log('==============================');
    console.log('1. Type "google.com" in address bar and press Enter');
    console.log('2. Type "test search" in address bar and press Enter');
    console.log('3. Click privacy panel button (⚙) and verify it opens');
    console.log('4. Look for "Search Engine" section in privacy panel');
    console.log('5. Try changing search provider in dropdown');
    console.log('6. Click "Configure Network" and verify no errors');
    
    return testResults;
}

// Auto-run tests
runProductionBuildTests();

// Export for manual use
window.productionBuildTests = {
    runAll: runProductionBuildTests,
    testApplicationStartup,
    testSearchEngine,
    testNetworkConfiguration,
    testUIComponents,
    testNavigation,
    checkConsoleErrors,
    results: testResults
};

console.log('\n🔧 Available commands:');
console.log('- productionBuildTests.runAll() - Run all tests');
console.log('- productionBuildTests.results - View test results');
console.log('- Individual test functions available');
