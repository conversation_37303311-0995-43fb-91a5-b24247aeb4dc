export interface LogAnalysis {
    totalEntries: number;
    errorCount: number;
    warningCount: number;
    navigationAttempts: number;
    webviewErrors: number;
    networkErrors: number;
    searchEngineErrors: number;
    recentErrors: any[];
    commonIssues: string[];
}
export declare class LogViewer {
    private logsDir;
    constructor();
    getLogFiles(): string[];
    getLatestLogFile(): string | null;
    readLogFile(filename: string): string;
    analyzeLogFile(filename: string): LogAnalysis;
    generateDiagnosticReport(filename?: string): string;
    private generateRecommendations;
    exportDiagnosticReport(filename?: string): string;
    cleanOldLogs(daysToKeep?: number): number;
}
export declare const logViewer: LogViewer;
//# sourceMappingURL=LogViewer.d.ts.map