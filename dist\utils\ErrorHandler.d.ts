export interface ErrorResponse {
    success: false;
    error: string;
    details?: string;
    code?: string;
    timestamp?: number;
}
export interface SuccessResponse<T = any> {
    success: true;
    data?: T;
    message?: string;
    timestamp?: number;
}
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;
export declare enum ErrorCode {
    PRIVACY_SETTINGS_LOAD_FAILED = "PRIVACY_SETTINGS_LOAD_FAILED",
    PRIVACY_SETTINGS_SAVE_FAILED = "PRIVACY_SETTINGS_SAVE_FAILED",
    CLEAR_BROWSING_DATA_FAILED = "CLEAR_BROWSING_DATA_FAILED",
    SECURITY_SETTINGS_LOAD_FAILED = "SECURITY_SETTINGS_LOAD_FAILED",
    SECURITY_SETTINGS_SAVE_FAILED = "SECURITY_SETTINGS_SAVE_FAILED",
    SECURITY_DATA_CLEAR_FAILED = "SECURITY_DATA_CLEAR_FAILED",
    PROXY_SETTINGS_LOAD_FAILED = "PROXY_SETTINGS_LOAD_FAILED",
    PROXY_SETTINGS_SAVE_FAILED = "PROXY_SETTINGS_SAVE_FAILED",
    PROXY_CONNECTION_FAILED = "PROXY_CONNECTION_FAILED",
    PROXY_AUTH_FAILED = "PROXY_AUTH_FAILED",
    SEARCH_SETTINGS_LOAD_FAILED = "SEARCH_SETTINGS_LOAD_FAILED",
    SEARCH_SETTINGS_SAVE_FAILED = "SEARCH_SETTINGS_SAVE_FAILED",
    SEARCH_PROVIDER_INVALID = "SEARCH_PROVIDER_INVALID",
    STORAGE_READ_FAILED = "STORAGE_READ_FAILED",
    STORAGE_WRITE_FAILED = "STORAGE_WRITE_FAILED",
    STORAGE_CORRUPTED = "STORAGE_CORRUPTED",
    NETWORK_CONNECTION_FAILED = "NETWORK_CONNECTION_FAILED",
    NETWORK_TIMEOUT = "NETWORK_TIMEOUT",
    INVALID_INPUT = "INVALID_INPUT",
    PERMISSION_DENIED = "PERMISSION_DENIED",
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
}
export declare class PhantomError extends Error {
    readonly code: ErrorCode;
    readonly details?: string;
    readonly timestamp: number;
    constructor(code: ErrorCode, message: string, details?: string);
    toResponse(): ErrorResponse;
}
export declare class ErrorHandler {
    private static instance;
    private constructor();
    static getInstance(): ErrorHandler;
    /**
     * Create a standardized error response
     */
    createErrorResponse(error: unknown, code?: ErrorCode, details?: string): ErrorResponse;
    /**
     * Create a standardized success response
     */
    createSuccessResponse<T>(data?: T, message?: string): SuccessResponse<T>;
    /**
     * Wrap async operations with standardized error handling
     */
    handleAsync<T>(operation: () => Promise<T>, errorCode: ErrorCode, errorMessage?: string): Promise<ApiResponse<T>>;
    /**
     * Wrap sync operations with standardized error handling
     */
    handleSync<T>(operation: () => T, errorCode: ErrorCode, errorMessage?: string): ApiResponse<T>;
    /**
     * Convert technical error messages to user-friendly messages
     */
    private getUserFriendlyMessage;
    /**
     * Clean up technical error messages for user display
     */
    private cleanTechnicalMessage;
    /**
     * Log error with context for debugging
     */
    logError(error: unknown, context: string, additionalInfo?: Record<string, any>): void;
    /**
     * Validate input and throw PhantomError if invalid
     */
    validateInput(condition: boolean, message: string, details?: string): void;
    /**
     * Create a timeout wrapper for promises
     */
    withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T>;
}
export default ErrorHandler;
//# sourceMappingURL=ErrorHandler.d.ts.map