// Systematic Browser Diagnosis Script
const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 Systematic Phantom Browser Diagnosis');
console.log('=======================================\n');

// Step 1: Check if browser process is running
async function checkBrowserProcess() {
    console.log('📊 Step 1: Checking Browser Process');
    console.log('-----------------------------------');
    
    return new Promise((resolve) => {
        exec('tasklist /FI "IMAGENAME eq phantom-browser-working.exe"', (error, stdout, stderr) => {
            if (error) {
                console.log('❌ Error checking process:', error.message);
                resolve(false);
                return;
            }
            
            const isRunning = stdout.includes('phantom-browser-working.exe');
            console.log(`Browser Process: ${isRunning ? '✅ Running' : '❌ Not Running'}`);
            
            if (isRunning) {
                // Extract process info
                const lines = stdout.split('\n');
                const processLine = lines.find(line => line.includes('phantom-browser-working.exe'));
                if (processLine) {
                    const parts = processLine.trim().split(/\s+/);
                    const pid = parts[1];
                    const memory = parts[4];
                    console.log(`   PID: ${pid}`);
                    console.log(`   Memory: ${memory}`);
                }
            }
            
            resolve(isRunning);
        });
    });
}

// Step 2: Check log directory and files
function checkLogSystem() {
    console.log('\n📁 Step 2: Checking Log System');
    console.log('------------------------------');
    
    const possibleLogPaths = [
        path.join(process.env.APPDATA, 'Phantom Browser', 'logs'),
        path.join(process.env.USERPROFILE, 'AppData', 'Roaming', 'Phantom Browser', 'logs'),
        path.join(__dirname, 'logs'),
        path.join(__dirname, 'executable-working', 'phantom-browser-working-win32-x64', 'logs')
    ];
    
    console.log('Checking possible log locations:');
    
    let foundLogs = false;
    
    possibleLogPaths.forEach((logPath, index) => {
        console.log(`${index + 1}. ${logPath}`);
        
        if (fs.existsSync(logPath)) {
            console.log('   ✅ Directory exists');
            
            try {
                const files = fs.readdirSync(logPath);
                const logFiles = files.filter(file => file.endsWith('.log'));
                
                console.log(`   📄 Found ${logFiles.length} log files`);
                
                if (logFiles.length > 0) {
                    foundLogs = true;
                    const latestLog = logFiles.sort().pop();
                    console.log(`   📝 Latest: ${latestLog}`);
                    
                    // Read and analyze latest log
                    const logContent = fs.readFileSync(path.join(logPath, latestLog), 'utf8');
                    const lines = logContent.split('\n').filter(line => line.trim());
                    
                    console.log(`   📊 Total log entries: ${lines.length}`);
                    
                    // Count by category
                    const categories = ['STARTUP', 'SEARCH', 'NAVIGATION', 'WEBVIEW', 'NETWORK', 'ERROR'];
                    categories.forEach(cat => {
                        const count = lines.filter(line => line.includes(`[${cat}]`)).length;
                        if (count > 0) {
                            console.log(`   ${cat}: ${count} entries`);
                        }
                    });
                    
                    // Show recent entries
                    console.log('\n   📝 Recent log entries:');
                    lines.slice(-5).forEach(line => {
                        if (line.trim()) {
                            console.log(`      ${line}`);
                        }
                    });
                }
            } catch (error) {
                console.log(`   ❌ Error reading directory: ${error.message}`);
            }
        } else {
            console.log('   ❌ Directory does not exist');
        }
    });
    
    return foundLogs;
}

// Step 3: Check browser executable and dependencies
function checkBrowserExecutable() {
    console.log('\n🔧 Step 3: Checking Browser Executable');
    console.log('--------------------------------------');
    
    const executablePath = path.join(__dirname, 'executable-working', 'phantom-browser-working-win32-x64', 'phantom-browser-working.exe');
    
    console.log(`Executable path: ${executablePath}`);
    
    if (fs.existsSync(executablePath)) {
        console.log('✅ Executable exists');
        
        const stats = fs.statSync(executablePath);
        console.log(`   Size: ${Math.round(stats.size / 1024 / 1024)} MB`);
        console.log(`   Modified: ${stats.mtime.toISOString()}`);
        
        // Check key files
        const keyFiles = [
            'resources/app/dist/main.js',
            'resources/app/dist/utils/DiagnosticLogger.js',
            'resources/app/dist/utils/LogViewer.js',
            'resources/app/renderer/renderer.js'
        ];
        
        console.log('\n   📁 Key files:');
        keyFiles.forEach(file => {
            const filePath = path.join(path.dirname(executablePath), file);
            const exists = fs.existsSync(filePath);
            console.log(`   ${exists ? '✅' : '❌'} ${file}`);
            
            if (exists && file.includes('DiagnosticLogger')) {
                // Check if DiagnosticLogger has expected content
                const content = fs.readFileSync(filePath, 'utf8');
                const hasLogging = content.includes('logStartup') && content.includes('logNavigation');
                console.log(`      ${hasLogging ? '✅' : '❌'} Contains logging methods`);
            }
        });
        
        return true;
    } else {
        console.log('❌ Executable does not exist');
        return false;
    }
}

// Step 4: Attempt to launch browser with detailed monitoring
async function launchBrowserWithMonitoring() {
    console.log('\n🚀 Step 4: Launching Browser with Monitoring');
    console.log('--------------------------------------------');
    
    const executablePath = path.join(__dirname, 'executable-working', 'phantom-browser-working-win32-x64', 'phantom-browser-working.exe');
    
    return new Promise((resolve) => {
        console.log('Launching browser...');
        
        const browser = spawn(executablePath, [], {
            detached: true,
            stdio: ['ignore', 'pipe', 'pipe']
        });
        
        let output = '';
        let errorOutput = '';
        
        browser.stdout.on('data', (data) => {
            output += data.toString();
            console.log('STDOUT:', data.toString().trim());
        });
        
        browser.stderr.on('data', (data) => {
            errorOutput += data.toString();
            console.log('STDERR:', data.toString().trim());
        });
        
        browser.on('error', (error) => {
            console.log('❌ Launch error:', error.message);
            resolve({ success: false, error: error.message });
        });
        
        browser.on('spawn', () => {
            console.log('✅ Browser process spawned');
            
            // Wait a moment then check if it's still running
            setTimeout(async () => {
                const isRunning = await checkBrowserProcess();
                resolve({ 
                    success: isRunning, 
                    output: output, 
                    errorOutput: errorOutput,
                    pid: browser.pid 
                });
            }, 3000);
        });
        
        // Timeout after 10 seconds
        setTimeout(() => {
            if (!browser.killed) {
                console.log('⏰ Launch timeout - checking status...');
                resolve({ 
                    success: false, 
                    error: 'Launch timeout',
                    output: output,
                    errorOutput: errorOutput
                });
            }
        }, 10000);
    });
}

// Main diagnostic workflow
async function runDiagnosis() {
    try {
        // Step 1: Check if browser is already running
        const isRunning = await checkBrowserProcess();
        
        // Step 2: Check log system
        const hasLogs = checkLogSystem();
        
        // Step 3: Check executable
        const hasExecutable = checkBrowserExecutable();
        
        // Step 4: If not running, try to launch
        let launchResult = null;
        if (!isRunning && hasExecutable) {
            launchResult = await launchBrowserWithMonitoring();
        }
        
        // Summary
        console.log('\n🎯 Diagnosis Summary');
        console.log('===================');
        console.log(`Browser Running: ${isRunning ? '✅' : '❌'}`);
        console.log(`Log System: ${hasLogs ? '✅ Working' : '❌ No logs found'}`);
        console.log(`Executable: ${hasExecutable ? '✅ Present' : '❌ Missing'}`);
        
        if (launchResult) {
            console.log(`Launch Attempt: ${launchResult.success ? '✅ Success' : '❌ Failed'}`);
            if (launchResult.error) {
                console.log(`Launch Error: ${launchResult.error}`);
            }
        }
        
        // Recommendations
        console.log('\n💡 Recommendations');
        console.log('==================');
        
        if (!hasLogs && isRunning) {
            console.log('- Browser is running but no logs found - logging system may not be initialized');
            console.log('- Check browser console (F12) for real-time logging');
        }
        
        if (!isRunning) {
            console.log('- Browser is not running - try manual launch');
            console.log('- Check for error messages in browser startup');
        }
        
        if (hasLogs) {
            console.log('- Logging system is working - proceed with navigation testing');
            console.log('- Use diagnostic tools in browser console');
        }
        
        console.log('\n📋 Next Steps');
        console.log('=============');
        console.log('1. If browser is running: Open Developer Tools (F12)');
        console.log('2. Check console for startup logs with emojis');
        console.log('3. Test navigation: type "example.com" in address bar');
        console.log('4. Monitor for error patterns in console');
        console.log('5. Generate diagnostic report: window.phantomBrowser.showDiagnosticReport()');
        
    } catch (error) {
        console.error('❌ Diagnosis failed:', error.message);
    }
}

// Run the diagnosis
runDiagnosis();
