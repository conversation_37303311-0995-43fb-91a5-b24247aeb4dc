{"version": 3, "file": "SettingsStore.d.ts", "sourceRoot": "", "sources": ["../../src/utils/SettingsStore.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,WAAW;IACxB,OAAO,EAAE;QACL,aAAa,EAAE,OAAO,CAAC;QACvB,QAAQ,EAAE,OAAO,CAAC;QAClB,mBAAmB,EAAE,OAAO,CAAC;QAC7B,aAAa,EAAE,OAAO,CAAC;QACvB,aAAa,EAAE,OAAO,CAAC;QACvB,WAAW,EAAE,OAAO,CAAC;QACrB,0BAA0B,EAAE,OAAO,CAAC;QACpC,WAAW,EAAE,OAAO,CAAC;QACrB,kBAAkB,EAAE,OAAO,CAAC;QAC5B,MAAM,EAAE,OAAO,CAAC;KACnB,CAAC;IACF,QAAQ,EAAE;QACN,aAAa,EAAE,OAAO,CAAC;QACvB,uBAAuB,EAAE,OAAO,CAAC;QACjC,SAAS,EAAE,OAAO,CAAC;QACnB,iBAAiB,EAAE,OAAO,CAAC;QAC3B,UAAU,EAAE,OAAO,CAAC;QACpB,YAAY,EAAE,OAAO,CAAC;QACtB,sBAAsB,EAAE,OAAO,CAAC;QAChC,eAAe,EAAE,OAAO,CAAC;KAC5B,CAAC;IACF,KAAK,EAAE;QACH,YAAY,EAAE;YACV,IAAI,EAAE,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;YACxD,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,OAAO,EAAE,OAAO,CAAC;SACpB,GAAG,IAAI,CAAC;QACT,SAAS,EAAE,KAAK,CAAC;YACb,IAAI,EAAE,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;YACxD,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,OAAO,EAAE,OAAO,CAAC;YACjB,IAAI,CAAC,EAAE,MAAM,CAAC;YACd,WAAW,CAAC,EAAE,MAAM,CAAC;SACxB,CAAC,CAAC;QACH,eAAe,EAAE,OAAO,CAAC;QACzB,gBAAgB,EAAE,MAAM,CAAC;KAC5B,CAAC;IACF,MAAM,EAAE;QACJ,eAAe,EAAE,MAAM,CAAC;QACxB,iBAAiB,EAAE,OAAO,CAAC;QAC3B,aAAa,EAAE,OAAO,CAAC;QACvB,cAAc,EAAE,MAAM,CAAC;QACvB,WAAW,EAAE,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC;QAChD,eAAe,EAAE,KAAK,CAAC;YACnB,EAAE,EAAE,MAAM,CAAC;YACX,IAAI,EAAE,MAAM,CAAC;YACb,OAAO,EAAE,MAAM,CAAC;YAChB,SAAS,EAAE,MAAM,CAAC;YAClB,cAAc,CAAC,EAAE,MAAM,CAAC;YACxB,IAAI,CAAC,EAAE,MAAM,CAAC;YACd,aAAa,EAAE,MAAM,CAAC;YACtB,QAAQ,EAAE,MAAM,EAAE,CAAC;YACnB,WAAW,EAAE,MAAM,CAAC;SACvB,CAAC,CAAC;KACN,CAAC;IACF,cAAc,EAAE;QACZ,wBAAwB,EAAE,OAAO,CAAC;QAClC,yBAAyB,EAAE,OAAO,CAAC;QACnC,qBAAqB,EAAE,OAAO,CAAC;QAC/B,kBAAkB,EAAE,OAAO,CAAC;QAC5B,oBAAoB,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC;QAC5D,gBAAgB,EAAE,OAAO,CAAC;QAC1B,uBAAuB,EAAE,OAAO,CAAC;QACjC,+BAA+B,EAAE,OAAO,CAAC;KAC5C,CAAC;CACL;AAED,qBAAa,aAAa;IACtB,OAAO,CAAC,KAAK,CAAqB;IAClC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAgB;IAEvC,OAAO;WAsEO,WAAW,IAAI,aAAa;IAO1C,OAAO,CAAC,kBAAkB;IA6DnB,GAAG,CAAC,CAAC,SAAS,MAAM,WAAW,EAAE,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;IASxD,GAAG,CAAC,CAAC,SAAS,MAAM,WAAW,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;IAUrE,GAAG,CAAC,CAAC,SAAS,MAAM,WAAW,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO;IAIjD,MAAM,CAAC,CAAC,SAAS,MAAM,WAAW,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI;IAUjD,KAAK,IAAI,IAAI;IAUb,YAAY,IAAI,MAAM;IAItB,KAAK,IAAI,IAAI;IAWb,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI;IAKtE,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI;IAKxE,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI;IAKlE,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI;IAKpE,4BAA4B,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI;CAI9F;AAED,eAAe,aAAa,CAAC"}