"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BehavioralAI = void 0;
const crypto = __importStar(require("crypto"));
class BehavioralAI {
    constructor() {
        this.trainingData = [];
        this.learnedPatterns = [];
        this.currentPattern = null;
        this.adaptationThreshold = 0.1;
        this.maxTrainingData = 10000;
        this.neuralNetwork = new SimpleNeuralNetwork([20, 15, 10, 5]); // Input, hidden layers, output
        this.initializeBasePatterns();
    }
    async initialize() {
        try {
            console.log('🧠 Initializing AI-Powered Behavioral Simulation...');
            await this.loadTrainingData().catch(error => {
                console.warn('Failed to load training data, using defaults:', error.message);
            });
            await this.trainInitialModel().catch(error => {
                console.warn('Failed to train initial model, using base patterns:', error.message);
            });
            this.startContinuousLearning();
            console.log('✅ AI-Powered Behavioral Simulation initialized successfully');
        }
        catch (error) {
            console.error('❌ Critical error in AI Behavioral Simulation initialization:', error);
            // Initialize with minimal functionality
            this.initializeBasePatterns();
            throw new Error(`AI Behavioral Simulation initialization failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    initializeBasePatterns() {
        // Initialize with realistic base patterns
        this.learnedPatterns = [
            {
                id: 'casual_user',
                name: 'Casual User',
                confidence: 0.8,
                features: {
                    avgActionInterval: 3000,
                    preferredTimeSlots: [19, 20, 21], // Evening hours
                    commonSequences: [['scroll', 'click'], ['type', 'pause', 'type']],
                    typingProfile: {
                        avgSpeed: 45,
                        burstPatterns: [2000, 1500, 3000],
                        pausePatterns: [500, 1000, 2000],
                        errorRate: 0.05,
                        correctionDelay: 800
                    },
                    mouseProfile: {
                        avgVelocity: 120,
                        accelerationPattern: [0.2, 0.5, 0.8, 1.0, 0.8],
                        jitterLevel: 2.5,
                        clickPrecision: 0.9,
                        dwellTime: 300
                    },
                    scrollProfile: {
                        avgSpeed: 150,
                        scrollPattern: 'smooth',
                        directionBias: 0.7,
                        pauseFrequency: 0.3
                    }
                },
                adaptationRate: 0.1,
                lastUpdated: Date.now()
            },
            {
                id: 'power_user',
                name: 'Power User',
                confidence: 0.85,
                features: {
                    avgActionInterval: 1200,
                    preferredTimeSlots: [9, 10, 14, 15], // Work hours
                    commonSequences: [['click', 'type', 'click'], ['scroll', 'scroll', 'click']],
                    typingProfile: {
                        avgSpeed: 85,
                        burstPatterns: [5000, 3000, 7000],
                        pausePatterns: [200, 400, 800],
                        errorRate: 0.02,
                        correctionDelay: 400
                    },
                    mouseProfile: {
                        avgVelocity: 200,
                        accelerationPattern: [0.3, 0.7, 1.0, 0.9, 0.6],
                        jitterLevel: 1.5,
                        clickPrecision: 0.95,
                        dwellTime: 150
                    },
                    scrollProfile: {
                        avgSpeed: 300,
                        scrollPattern: 'burst',
                        directionBias: 0.8,
                        pauseFrequency: 0.2
                    }
                },
                adaptationRate: 0.05,
                lastUpdated: Date.now()
            }
        ];
    }
    recordBehavior(action, duration, metrics = {}) {
        const dataPoint = {
            timestamp: Date.now(),
            action,
            duration,
            context: {
                timeOfDay: new Date().getHours(),
                dayOfWeek: new Date().getDay(),
                sessionLength: this.getSessionLength(),
                previousActions: this.getRecentActions(5)
            },
            metrics
        };
        this.trainingData.push(dataPoint);
        // Limit training data size
        if (this.trainingData.length > this.maxTrainingData) {
            this.trainingData = this.trainingData.slice(-this.maxTrainingData);
        }
        // Trigger adaptation if enough new data
        if (this.trainingData.length % 100 === 0) {
            this.adaptBehavior();
        }
    }
    async loadTrainingData() {
        // In a real implementation, this would load from secure storage
        // For now, we'll simulate with some realistic data
        this.generateSyntheticTrainingData();
    }
    generateSyntheticTrainingData() {
        const actions = ['scroll', 'click', 'type', 'pause', 'navigate', 'hover'];
        const now = Date.now();
        for (let i = 0; i < 1000; i++) {
            const action = actions[Math.floor(Math.random() * actions.length)];
            const duration = this.generateRealisticDuration(action);
            this.trainingData.push({
                timestamp: now - (i * 60000), // Spread over last 1000 minutes
                action,
                duration,
                context: {
                    timeOfDay: Math.floor(Math.random() * 24),
                    dayOfWeek: Math.floor(Math.random() * 7),
                    sessionLength: Math.random() * 7200000, // Up to 2 hours
                    previousActions: this.generateRandomActionSequence()
                },
                metrics: this.generateRealisticMetrics(action)
            });
        }
    }
    generateRealisticDuration(action) {
        const baseDurations = {
            scroll: 500,
            click: 100,
            type: 2000,
            pause: 1500,
            navigate: 3000,
            hover: 800
        };
        const base = baseDurations[action] || 1000;
        return base + (Math.random() - 0.5) * base * 0.5; // ±25% variation
    }
    generateRealisticMetrics(action) {
        switch (action) {
            case 'type':
                return {
                    typingCadence: 40 + Math.random() * 60, // 40-100 CPM
                };
            case 'scroll':
                return {
                    scrollSpeed: 100 + Math.random() * 200, // 100-300 px/s
                };
            case 'click':
                return {
                    mouseVelocity: 80 + Math.random() * 120, // 80-200 px/s
                };
            default:
                return {};
        }
    }
    generateRandomActionSequence() {
        const actions = ['scroll', 'click', 'type', 'pause'];
        const length = Math.floor(Math.random() * 5) + 1;
        return Array.from({ length }, () => actions[Math.floor(Math.random() * actions.length)]);
    }
    async trainInitialModel() {
        if (this.trainingData.length < 100)
            return;
        // Prepare training data for neural network
        const trainingSet = this.prepareTrainingSet();
        // Train the neural network
        await this.neuralNetwork.train(trainingSet, {
            epochs: 1000,
            learningRate: 0.01,
            momentum: 0.9
        });
        console.log('Initial behavioral model trained');
    }
    prepareTrainingSet() {
        return this.trainingData.map(dataPoint => ({
            input: this.extractFeatures(dataPoint),
            output: this.encodeAction(dataPoint.action)
        }));
    }
    extractFeatures(dataPoint) {
        // Extract 20 features for neural network input
        return [
            dataPoint.context.timeOfDay / 24,
            dataPoint.context.dayOfWeek / 7,
            dataPoint.context.sessionLength / 7200000, // Normalize to 2 hours
            dataPoint.duration / 10000, // Normalize duration
            dataPoint.metrics.mouseVelocity || 0 / 300, // Normalize velocity
            dataPoint.metrics.scrollSpeed || 0 / 500, // Normalize scroll speed
            dataPoint.metrics.typingCadence || 0 / 120, // Normalize typing speed
            ...this.encodeActionSequence(dataPoint.context.previousActions), // 13 features
        ];
    }
    encodeAction(action) {
        const actions = ['scroll', 'click', 'type', 'pause', 'navigate', 'hover'];
        const encoded = new Array(actions.length).fill(0);
        const index = actions.indexOf(action);
        if (index !== -1)
            encoded[index] = 1;
        return encoded;
    }
    encodeActionSequence(actions) {
        // Encode last 5 actions as binary features (13 features total)
        const actionTypes = ['scroll', 'click', 'type', 'pause', 'navigate'];
        const encoded = new Array(13).fill(0);
        actions.slice(-5).forEach((action, index) => {
            const actionIndex = actionTypes.indexOf(action);
            if (actionIndex !== -1 && index < 2) {
                encoded[index * 5 + actionIndex] = 1;
            }
        });
        // Add sequence length and diversity
        encoded[10] = Math.min(actions.length / 5, 1);
        encoded[11] = new Set(actions).size / actionTypes.length;
        encoded[12] = actions.length > 0 ? 1 : 0;
        return encoded;
    }
    startContinuousLearning() {
        // Retrain model every 10 minutes with new data
        setInterval(() => {
            if (this.trainingData.length > 50) {
                this.adaptBehavior();
            }
        }, 600000); // 10 minutes
    }
    async adaptBehavior() {
        // Analyze recent behavior patterns
        const recentData = this.trainingData.slice(-200); // Last 200 actions
        const newPattern = this.analyzePatterns(recentData);
        if (newPattern && this.shouldAdaptToPattern(newPattern)) {
            await this.updateBehavioralModel(newPattern);
            console.log(`Adapted to new behavioral pattern: ${newPattern.name}`);
        }
    }
    analyzePatterns(data) {
        if (data.length < 50)
            return null;
        // Analyze timing patterns
        const intervals = data.slice(1).map((point, i) => point.timestamp - data[i].timestamp);
        const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
        // Analyze action sequences
        const sequences = this.extractActionSequences(data);
        // Analyze time preferences
        const timeSlots = data.map(d => d.context.timeOfDay);
        const preferredTimes = this.findPreferredTimeSlots(timeSlots);
        // Create new pattern
        return {
            id: crypto.randomBytes(8).toString('hex'),
            name: `Adaptive Pattern ${Date.now()}`,
            confidence: 0.7,
            features: {
                avgActionInterval: avgInterval,
                preferredTimeSlots: preferredTimes,
                commonSequences: sequences,
                typingProfile: this.analyzeTypingProfile(data),
                mouseProfile: this.analyzeMouseProfile(data),
                scrollProfile: this.analyzeScrollProfile(data)
            },
            adaptationRate: 0.15,
            lastUpdated: Date.now()
        };
    }
    extractActionSequences(data) {
        const sequences = [];
        for (let i = 0; i < data.length - 2; i++) {
            sequences.push([
                data[i].action,
                data[i + 1].action,
                data[i + 2].action
            ]);
        }
        return sequences;
    }
    findPreferredTimeSlots(timeSlots) {
        const hourCounts = new Array(24).fill(0);
        timeSlots.forEach(hour => hourCounts[hour]++);
        const avgCount = hourCounts.reduce((a, b) => a + b, 0) / 24;
        return hourCounts
            .map((count, hour) => ({ hour, count }))
            .filter(({ count }) => count > avgCount * 1.2)
            .map(({ hour }) => hour);
    }
    analyzeTypingProfile(data) {
        const typingData = data.filter(d => d.action === 'type');
        return {
            avgSpeed: this.calculateAverage(typingData.map(d => d.metrics.typingCadence || 50)),
            burstPatterns: typingData.map(d => d.duration),
            pausePatterns: this.calculatePauseBetweenActions(typingData),
            errorRate: 0.03 + Math.random() * 0.04, // 3-7%
            correctionDelay: 600 + Math.random() * 400 // 600-1000ms
        };
    }
    analyzeMouseProfile(data) {
        const mouseData = data.filter(d => d.metrics.mouseVelocity);
        return {
            avgVelocity: this.calculateAverage(mouseData.map(d => d.metrics.mouseVelocity || 150)),
            accelerationPattern: [0.2, 0.5, 0.8, 1.0, 0.7], // Default curve
            jitterLevel: 1.5 + Math.random() * 2, // 1.5-3.5
            clickPrecision: 0.85 + Math.random() * 0.1, // 85-95%
            dwellTime: 200 + Math.random() * 300 // 200-500ms
        };
    }
    analyzeScrollProfile(data) {
        const scrollData = data.filter(d => d.action === 'scroll');
        return {
            avgSpeed: this.calculateAverage(scrollData.map(d => d.metrics.scrollSpeed || 200)),
            scrollPattern: ['smooth', 'jerky', 'burst', 'variable'][Math.floor(Math.random() * 4)],
            directionBias: 0.6 + Math.random() * 0.3, // 60-90% down
            pauseFrequency: 0.2 + Math.random() * 0.3 // 20-50%
        };
    }
    calculateAverage(values) {
        return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
    }
    calculatePauseBetweenActions(data) {
        const pauses = [];
        for (let i = 1; i < data.length; i++) {
            pauses.push(data[i].timestamp - data[i - 1].timestamp);
        }
        return pauses;
    }
    shouldAdaptToPattern(newPattern) {
        if (!this.currentPattern)
            return true;
        // Adapt if the new pattern is significantly different
        const timeDiff = Math.abs(newPattern.features.avgActionInterval - this.currentPattern.features.avgActionInterval);
        const threshold = this.currentPattern.features.avgActionInterval * this.adaptationThreshold;
        return timeDiff > threshold;
    }
    async updateBehavioralModel(newPattern) {
        // Add to learned patterns
        this.learnedPatterns.push(newPattern);
        // Keep only the most recent patterns
        if (this.learnedPatterns.length > 10) {
            this.learnedPatterns = this.learnedPatterns
                .sort((a, b) => b.lastUpdated - a.lastUpdated)
                .slice(0, 10);
        }
        // Update current pattern
        this.currentPattern = newPattern;
        // Retrain neural network with new pattern
        const recentTrainingSet = this.prepareTrainingSet().slice(-500);
        await this.neuralNetwork.partialTrain(recentTrainingSet, {
            epochs: 100,
            learningRate: 0.005
        });
    }
    predictNextAction(context) {
        const features = this.extractContextFeatures(context);
        const prediction = this.neuralNetwork.predict(features);
        const actions = ['scroll', 'click', 'type', 'pause', 'navigate', 'hover'];
        const maxIndex = prediction.indexOf(Math.max(...prediction));
        return {
            action: actions[maxIndex] || 'pause',
            confidence: prediction[maxIndex],
            timing: this.predictTiming(context)
        };
    }
    extractContextFeatures(context) {
        return [
            (context.timeOfDay || new Date().getHours()) / 24,
            (context.dayOfWeek || new Date().getDay()) / 7,
            (context.sessionLength || 0) / 7200000,
            (context.lastActionDuration || 1000) / 10000,
            0, 0, 0, // Placeholder for metrics
            ...this.encodeActionSequence(context.previousActions || [])
        ];
    }
    predictTiming(context) {
        if (!this.currentPattern)
            return 2000;
        const baseInterval = this.currentPattern.features.avgActionInterval;
        const timeOfDay = context.timeOfDay || new Date().getHours();
        // Adjust based on preferred time slots
        const isPreferredTime = this.currentPattern.features.preferredTimeSlots.includes(timeOfDay);
        const timingMultiplier = isPreferredTime ? 0.8 : 1.2;
        return baseInterval * timingMultiplier * (0.8 + Math.random() * 0.4);
    }
    getCurrentPattern() {
        return this.currentPattern;
    }
    getSessionLength() {
        if (this.trainingData.length === 0)
            return 0;
        const firstAction = this.trainingData[0].timestamp;
        return Date.now() - firstAction;
    }
    getRecentActions(count) {
        return this.trainingData
            .slice(-count)
            .map(d => d.action);
    }
    getAdaptationMetrics() {
        return {
            totalPatterns: this.learnedPatterns.length,
            currentConfidence: this.currentPattern?.confidence || 0,
            adaptationRate: this.currentPattern?.adaptationRate || 0,
            trainingDataSize: this.trainingData.length
        };
    }
}
exports.BehavioralAI = BehavioralAI;
// Simple Neural Network implementation for behavioral prediction
class SimpleNeuralNetwork {
    constructor(layers) {
        this.layers = layers;
        this.weights = [];
        this.biases = [];
        this.initializeWeights();
    }
    initializeWeights() {
        for (let i = 0; i < this.layers.length - 1; i++) {
            const layerWeights = [];
            const layerBiases = [];
            for (let j = 0; j < this.layers[i + 1]; j++) {
                const neuronWeights = [];
                for (let k = 0; k < this.layers[i]; k++) {
                    neuronWeights.push((Math.random() - 0.5) * 2);
                }
                layerWeights.push(neuronWeights);
                layerBiases.push((Math.random() - 0.5) * 2);
            }
            this.weights.push(layerWeights);
            this.biases.push(layerBiases);
        }
    }
    predict(input) {
        let activation = input;
        for (let i = 0; i < this.weights.length; i++) {
            const newActivation = [];
            for (let j = 0; j < this.weights[i].length; j++) {
                let sum = this.biases[i][j];
                for (let k = 0; k < activation.length; k++) {
                    sum += activation[k] * this.weights[i][j][k];
                }
                newActivation.push(this.sigmoid(sum));
            }
            activation = newActivation;
        }
        return activation;
    }
    async train(trainingSet, options) {
        // Simplified training implementation
        for (let epoch = 0; epoch < options.epochs; epoch++) {
            for (const sample of trainingSet) {
                this.backpropagate(sample.input, sample.output, options.learningRate);
            }
        }
    }
    async partialTrain(trainingSet, options) {
        await this.train(trainingSet, options);
    }
    backpropagate(input, target, learningRate) {
        // Simplified backpropagation
        const prediction = this.predict(input);
        const error = target.map((t, i) => t - prediction[i]);
        // Update weights (simplified)
        for (let i = this.weights.length - 1; i >= 0; i--) {
            for (let j = 0; j < this.weights[i].length; j++) {
                for (let k = 0; k < this.weights[i][j].length; k++) {
                    this.weights[i][j][k] += learningRate * error[j] * 0.1;
                }
                this.biases[i][j] += learningRate * error[j] * 0.1;
            }
        }
    }
    sigmoid(x) {
        return 1 / (1 + Math.exp(-x));
    }
}
//# sourceMappingURL=BehavioralAI.js.map