"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchEngine = void 0;
const SettingsStore_1 = require("../utils/SettingsStore");
class SearchEngine {
    constructor() {
        this.providers = new Map();
        this.searchHistory = [];
        this.maxHistorySize = 1000;
        this.settingsStore = SettingsStore_1.SettingsStore.getInstance();
        this.initializeDefaultProviders();
        this.loadSettings();
    }
    initializeDefaultProviders() {
        const defaultProviders = [
            {
                id: 'duckduckgo',
                name: 'DuckDuckGo',
                baseUrl: 'https://duckduckgo.com',
                searchUrl: 'https://duckduckgo.com/?q={query}&t=phantom',
                suggestionsUrl: 'https://duckduckgo.com/ac/?q={query}&type=list',
                icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40NzcgMiAyIDYuNDc3IDIgMTJTNi40NzcgMjIgMTIgMjJTMjIgMTcuNTIzIDIyIDEyUzE3LjUyMyAyIDEyIDJaIiBmaWxsPSIjREU1ODMzIi8+CjxwYXRoIGQ9Ik0xMiA2QzE1LjMxNCA2IDE4IDguNjg2IDE4IDEyUzE1LjMxNCAxOCAxMiAxOFM2IDE1LjMxNCA2IDEyUzguNjg2IDYgMTIgNloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=',
                privacyRating: 10,
                features: ['No tracking', 'No ads', 'Tor support', 'Instant answers'],
                description: 'Privacy-focused search engine with no tracking'
            },
            {
                id: 'startpage',
                name: 'Startpage',
                baseUrl: 'https://www.startpage.com',
                searchUrl: 'https://www.startpage.com/sp/search?query={query}&cat=web&pl=phantom',
                suggestionsUrl: 'https://www.startpage.com/cgi-bin/csuggest?query={query}&limit=10',
                privacyRating: 9,
                features: ['Google results', 'No tracking', 'Anonymous view', 'EU based'],
                description: 'Private search with Google results, no tracking'
            },
            {
                id: 'searx',
                name: 'SearX',
                baseUrl: 'https://searx.org',
                searchUrl: 'https://searx.org/search?q={query}&categories=general',
                privacyRating: 10,
                features: ['Open source', 'No tracking', 'Aggregated results', 'Self-hostable'],
                description: 'Open source metasearch engine'
            },
            {
                id: 'brave',
                name: 'Brave Search',
                baseUrl: 'https://search.brave.com',
                searchUrl: 'https://search.brave.com/search?q={query}&source=phantom',
                suggestionsUrl: 'https://search.brave.com/api/suggest?q={query}',
                privacyRating: 8,
                features: ['Independent index', 'No tracking', 'Ad-free', 'Fast results'],
                description: 'Independent search engine by Brave'
            },
            {
                id: 'yandex',
                name: 'Yandex',
                baseUrl: 'https://yandex.com',
                searchUrl: 'https://yandex.com/search/?text={query}&lr=0',
                suggestionsUrl: 'https://suggest.yandex.com/suggest-ff.cgi?part={query}',
                privacyRating: 4,
                features: ['Russian focus', 'Image search', 'Maps integration', 'Translation'],
                description: 'Russian search engine with global reach'
            },
            {
                id: 'bing',
                name: 'Bing',
                baseUrl: 'https://www.bing.com',
                searchUrl: 'https://www.bing.com/search?q={query}&form=phantom',
                suggestionsUrl: 'https://www.bing.com/AS/Suggestions?pt=page.home&mkt=en-us&qry={query}',
                privacyRating: 3,
                features: ['Microsoft integration', 'Visual search', 'News integration', 'Rewards'],
                description: 'Microsoft search engine with visual features'
            }
        ];
        defaultProviders.forEach(provider => {
            this.providers.set(provider.id, provider);
        });
    }
    getDefaultSettings() {
        return {
            defaultProvider: 'duckduckgo',
            enableSuggestions: true,
            enableHistory: true,
            maxSuggestions: 8,
            privacyMode: 'balanced',
            customProviders: []
        };
    }
    loadSettings() {
        try {
            this.settings = this.settingsStore.get('search');
            console.log('Search engine settings loaded from storage');
        }
        catch (error) {
            console.error('Failed to load search settings:', error);
            // Use default settings if loading fails
            this.settings = this.getDefaultSettings();
        }
    }
    getProviders() {
        return Array.from(this.providers.values());
    }
    getProvider(id) {
        return this.providers.get(id);
    }
    getDefaultProvider() {
        return this.providers.get(this.settings.defaultProvider) || this.providers.get('duckduckgo');
    }
    getDefaultHomepage() {
        const defaultProvider = this.getDefaultProvider();
        return defaultProvider.baseUrl;
    }
    setDefaultProvider(providerId) {
        if (this.providers.has(providerId)) {
            this.settings.defaultProvider = providerId;
            this.saveSettings();
        }
    }
    search(query, providerId) {
        const provider = providerId ? this.providers.get(providerId) : this.getDefaultProvider();
        if (!provider) {
            throw new Error('Search provider not found');
        }
        // Add to search history
        this.addToHistory(query, provider.id);
        // Replace query placeholder in URL
        const searchUrl = provider.searchUrl.replace('{query}', encodeURIComponent(query));
        console.log(`Searching "${query}" with ${provider.name}: ${searchUrl}`);
        return searchUrl;
    }
    async getSuggestions(query, providerId) {
        if (!this.settings.enableSuggestions || query.length < 2) {
            return [];
        }
        const suggestions = [];
        // Add history suggestions
        if (this.settings.enableHistory) {
            const historySuggestions = this.getHistorySuggestions(query);
            suggestions.push(...historySuggestions);
        }
        // Add provider suggestions
        const provider = providerId ? this.providers.get(providerId) : this.getDefaultProvider();
        if (provider?.suggestionsUrl) {
            try {
                const providerSuggestions = await this.fetchProviderSuggestions(query, provider);
                suggestions.push(...providerSuggestions);
            }
            catch (error) {
                console.warn('Failed to fetch suggestions:', error);
            }
        }
        // Limit results
        return suggestions.slice(0, this.settings.maxSuggestions);
    }
    async fetchProviderSuggestions(query, provider) {
        // This would make actual HTTP requests in a real implementation
        // For now, return mock suggestions
        const mockSuggestions = [
            `${query} tutorial`,
            `${query} guide`,
            `${query} examples`,
            `${query} documentation`
        ];
        return mockSuggestions.map(suggestion => ({
            query: suggestion,
            type: 'suggestion'
        }));
    }
    getHistorySuggestions(query) {
        const lowerQuery = query.toLowerCase();
        return this.searchHistory
            .filter(item => item.query.toLowerCase().includes(lowerQuery))
            .slice(0, 3)
            .map(item => ({
            query: item.query,
            type: 'history'
        }));
    }
    addToHistory(query, provider) {
        // Remove existing entry if present
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        // Add new entry at the beginning
        this.searchHistory.unshift({
            query,
            timestamp: Date.now(),
            provider
        });
        // Limit history size
        if (this.searchHistory.length > this.maxHistorySize) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
        }
    }
    clearHistory() {
        this.searchHistory = [];
        console.log('Search history cleared');
    }
    getSettings() {
        return { ...this.settings };
    }
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
    }
    saveSettings() {
        try {
            this.settingsStore.updateSearchSettings(this.settings);
            console.log('Search engine settings saved to storage');
        }
        catch (error) {
            console.error('Failed to save search settings:', error);
            throw new Error(`Failed to save search settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    addCustomProvider(provider) {
        this.providers.set(provider.id, provider);
        this.settings.customProviders.push(provider);
        this.saveSettings();
    }
    removeCustomProvider(providerId) {
        this.providers.delete(providerId);
        this.settings.customProviders = this.settings.customProviders.filter(p => p.id !== providerId);
        this.saveSettings();
    }
    getSearchHistory() {
        return [...this.searchHistory];
    }
    isValidUrl(input) {
        try {
            new URL(input);
            return true;
        }
        catch {
            // Check for domain-like patterns
            const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
            return domainPattern.test(input) || input.includes('.');
        }
    }
    processInput(input) {
        const trimmedInput = input.trim();
        if (this.isValidUrl(trimmedInput)) {
            // Add protocol if missing
            const url = trimmedInput.startsWith('http') ? trimmedInput : `https://${trimmedInput}`;
            return { type: 'url', value: url };
        }
        else {
            // Treat as search query
            const searchUrl = this.search(trimmedInput);
            return { type: 'search', value: searchUrl };
        }
    }
}
exports.SearchEngine = SearchEngine;
//# sourceMappingURL=SearchEngine.js.map