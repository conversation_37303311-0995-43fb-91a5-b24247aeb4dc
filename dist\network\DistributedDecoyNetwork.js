"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistributedDecoyNetwork = void 0;
const events_1 = require("events");
const crypto = __importStar(require("crypto"));
const BlockchainCoordinator_1 = require("./BlockchainCoordinator");
const P2PNetworking_1 = require("./P2PNetworking");
class DistributedDecoyNetwork extends events_1.EventEmitter {
    constructor() {
        super();
        this.knownNodes = new Map();
        this.activeConnections = new Map();
        this.coordinationPatterns = new Map();
        this.currentPattern = null;
        this.networkTopology = null;
        this.isCoordinator = false;
        this.heartbeatInterval = null;
        this.coordinationInterval = null;
        this.nodeId = crypto.randomBytes(16).toString('hex');
        this.generateKeyPair();
        this.initializeDecoyPatterns();
        this.blockchainCoordinator = new BlockchainCoordinator_1.BlockchainCoordinator(this.nodeId, this.privateKey);
        this.p2pNetwork = new P2PNetworking_1.P2PNetworking(this.nodeId, this.privateKey, this.publicKey);
        this.setupP2PHandlers();
    }
    async initialize() {
        await this.p2pNetwork.initialize();
        await this.discoverBootstrapNodes();
        await this.joinNetwork();
        this.startHeartbeat();
        this.startCoordination();
        console.log(`Distributed Decoy Network initialized - Node ID: ${this.nodeId}`);
    }
    generateKeyPair() {
        // Generate Ed25519 key pair for node authentication
        const keyPair = crypto.generateKeyPairSync('ed25519');
        this.privateKey = keyPair.privateKey.export({ type: 'pkcs8', format: 'der' });
        this.publicKey = keyPair.publicKey.export({ type: 'spki', format: 'der' });
    }
    setupP2PHandlers() {
        this.p2pNetwork.on('peerConnected', (peer) => {
            console.log(`P2P peer connected: ${peer.id}`);
            this.handlePeerJoin(peer);
        });
        this.p2pNetwork.on('peerDisconnected', (peer) => {
            console.log(`P2P peer disconnected: ${peer.id}`);
            this.handlePeerLeave(peer);
        });
        this.p2pNetwork.on('dataReceived', (data) => {
            this.handleP2PMessage(data);
        });
    }
    handlePeerJoin(peer) {
        // Add peer to known nodes
        const networkNode = {
            id: peer.id,
            publicKey: '', // Will be updated during handshake
            endpoint: `${peer.address}:${peer.port}`,
            lastSeen: Date.now(),
            reputation: 0.5, // Default reputation
            capabilities: {
                maxBandwidth: 500,
                supportedProtocols: ['https'],
                decoyTypes: ['social_media', 'news'],
                coordinationLevel: 'basic',
                uptime: 0.9
            }
        };
        this.knownNodes.set(peer.id, networkNode);
        this.blockchainCoordinator.addValidator(peer.id);
    }
    handlePeerLeave(peer) {
        this.knownNodes.delete(peer.id);
        this.blockchainCoordinator.removeValidator(peer.id);
    }
    handleP2PMessage(data) {
        // Handle coordination messages received via P2P
        try {
            const message = data.data;
            if (message.type === 'coordination') {
                this.handleCoordinationMessage(message);
            }
            else if (message.type === 'blockchain') {
                this.handleBlockchainMessage(message);
            }
        }
        catch (error) {
            console.warn('Failed to handle P2P message:', error);
        }
    }
    handleCoordinationMessage(message) {
        // Process coordination messages
        switch (message.subtype) {
            case 'pattern_proposal':
                this.handlePatternProposal(message);
                break;
            case 'pattern_execution':
                this.handlePatternExecution(message);
                break;
            case 'consensus_request':
                this.handleConsensusRequest(message);
                break;
        }
    }
    handleBlockchainMessage(message) {
        // Forward blockchain messages to blockchain coordinator
        if (message.subtype === 'block_proposal') {
            this.blockchainCoordinator.proposeBlock(message.block);
        }
        else if (message.subtype === 'consensus_vote') {
            this.blockchainCoordinator.voteOnBlock(message.blockHash, message.vote);
        }
    }
    handlePatternProposal(message) {
        console.log(`Received pattern proposal: ${message.payload.patternId}`);
        // Process pattern proposal
        const pattern = this.coordinationPatterns.get(message.payload.patternId);
        if (pattern) {
            // Evaluate if this node can participate
            const canParticipate = this.evaluatePatternParticipation(pattern);
            if (canParticipate) {
                console.log(`Accepting participation in pattern: ${pattern.name}`);
            }
        }
    }
    handlePatternExecution(message) {
        console.log(`Received pattern execution command: ${message.payload.pattern.name}`);
        // Execute the pattern if this node is a participant
        const participants = message.payload.participants || [];
        if (participants.includes(this.nodeId)) {
            const pattern = message.payload.pattern;
            const delay = message.payload.startTime - Date.now();
            if (delay > 0) {
                setTimeout(() => {
                    this.executePattern(pattern);
                }, delay);
            }
            else {
                this.executePattern(pattern);
            }
        }
    }
    handleConsensusRequest(message) {
        console.log(`Received consensus request for: ${message.payload.subject}`);
        // Participate in consensus voting
        const vote = this.evaluateConsensusVote(message.payload);
        // Send vote back via blockchain
        this.blockchainCoordinator.voteOnBlock(message.payload.blockHash, vote);
    }
    evaluatePatternParticipation(pattern) {
        // Simple evaluation - can be made more sophisticated
        return this.knownNodes.size >= pattern.participants;
    }
    evaluateConsensusVote(payload) {
        // Simple consensus evaluation - approve by default
        // In a real implementation, this would have sophisticated logic
        return 'approve';
    }
    initializeDecoyPatterns() {
        const patterns = [
            {
                id: 'social_media_burst',
                name: 'Social Media Burst',
                description: 'Simulates coordinated social media activity',
                participants: 5,
                duration: 300000, // 5 minutes
                traffic: {
                    type: 'https',
                    pattern: 'burst',
                    volume: 30,
                    targets: [
                        'https://httpbin.org/json',
                        'https://jsonplaceholder.typicode.com/posts',
                        'https://reqres.in/api/users'
                    ]
                },
                coordination: {
                    timing: 'synchronized',
                    offset: 1000,
                    variance: 20
                }
            },
            {
                id: 'news_reading_pattern',
                name: 'News Reading Pattern',
                description: 'Simulates distributed news consumption',
                participants: 10,
                duration: 600000, // 10 minutes
                traffic: {
                    type: 'https',
                    pattern: 'steady',
                    volume: 15,
                    targets: [
                        'https://httpbin.org/html',
                        'https://httpstat.us/200',
                        'https://jsonplaceholder.typicode.com/posts'
                    ]
                },
                coordination: {
                    timing: 'staggered',
                    offset: 5000,
                    variance: 30
                }
            },
            {
                id: 'video_streaming_sync',
                name: 'Video Streaming Sync',
                description: 'Coordinated video streaming simulation',
                participants: 8,
                duration: 900000, // 15 minutes
                traffic: {
                    type: 'https',
                    pattern: 'steady',
                    volume: 60,
                    targets: [
                        'https://httpbin.org/stream/20',
                        'https://httpbin.org/drip?duration=5&numbytes=1024'
                    ]
                },
                coordination: {
                    timing: 'synchronized',
                    offset: 500,
                    variance: 10
                }
            },
            {
                id: 'distributed_research',
                name: 'Distributed Research',
                description: 'Academic research browsing pattern',
                participants: 15,
                duration: 1800000, // 30 minutes
                traffic: {
                    type: 'mixed',
                    pattern: 'random',
                    volume: 8,
                    targets: [
                        'https://httpbin.org/json',
                        'https://jsonplaceholder.typicode.com/posts',
                        'https://httpstat.us/200'
                    ]
                },
                coordination: {
                    timing: 'random',
                    offset: 10000,
                    variance: 50
                }
            }
        ];
        patterns.forEach(pattern => {
            this.coordinationPatterns.set(pattern.id, pattern);
        });
    }
    async discoverBootstrapNodes() {
        // In a real implementation, this would connect to bootstrap servers
        // For now, we'll simulate discovery of other nodes
        const bootstrapNodes = [
            {
                id: 'bootstrap-1',
                publicKey: crypto.randomBytes(32).toString('hex'),
                endpoint: 'wss://phantom-network-1.example.com',
                lastSeen: Date.now(),
                reputation: 0.9,
                capabilities: {
                    maxBandwidth: 1000,
                    supportedProtocols: ['https', 'websocket'],
                    decoyTypes: ['social_media', 'news', 'video'],
                    coordinationLevel: 'expert',
                    uptime: 0.95
                },
                geolocation: {
                    country: 'US',
                    region: 'West',
                    timezone: 'PST'
                }
            },
            {
                id: 'bootstrap-2',
                publicKey: crypto.randomBytes(32).toString('hex'),
                endpoint: 'wss://phantom-network-2.example.com',
                lastSeen: Date.now(),
                reputation: 0.85,
                capabilities: {
                    maxBandwidth: 800,
                    supportedProtocols: ['https'],
                    decoyTypes: ['news', 'research'],
                    coordinationLevel: 'advanced',
                    uptime: 0.92
                },
                geolocation: {
                    country: 'DE',
                    region: 'Central',
                    timezone: 'CET'
                }
            }
        ];
        bootstrapNodes.forEach(node => {
            this.knownNodes.set(node.id, node);
        });
        console.log(`Discovered ${bootstrapNodes.length} bootstrap nodes`);
    }
    async joinNetwork() {
        // Connect to bootstrap nodes and announce presence
        for (const [nodeId, node] of this.knownNodes) {
            try {
                await this.connectToNode(node);
                await this.sendJoinMessage(node);
            }
            catch (error) {
                console.warn(`Failed to connect to node ${nodeId}:`, error);
            }
        }
        // Determine if this node should become a coordinator
        this.evaluateCoordinatorRole();
    }
    async connectToNode(node) {
        return new Promise((resolve, reject) => {
            // Simulate WebSocket connection
            // In a real implementation, this would create actual WebSocket connections
            const mockConnection = {
                send: (data) => {
                    console.log(`Sending to ${node.id}:`, JSON.parse(data).type);
                },
                close: () => {
                    this.activeConnections.delete(node.id);
                },
                readyState: 1 // OPEN
            };
            this.activeConnections.set(node.id, mockConnection);
            // Simulate connection events
            setTimeout(() => {
                this.emit('nodeConnected', node);
                resolve();
            }, 100 + Math.random() * 200);
        });
    }
    async sendJoinMessage(node) {
        const message = {
            type: 'join',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            signature: '',
            payload: {
                publicKey: this.publicKey.toString('hex'),
                capabilities: {
                    maxBandwidth: 500,
                    supportedProtocols: ['https', 'websocket'],
                    decoyTypes: ['social_media', 'news', 'video', 'research'],
                    coordinationLevel: 'advanced',
                    uptime: 0.98
                },
                geolocation: {
                    country: 'Unknown',
                    region: 'Unknown',
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                }
            }
        };
        message.signature = this.signMessage(message);
        await this.sendMessage(node.id, message);
    }
    signMessage(message) {
        const messageData = JSON.stringify({
            type: message.type,
            nodeId: message.nodeId,
            timestamp: message.timestamp,
            payload: message.payload
        });
        return crypto.createHash('sha256').update(messageData + this.privateKey.toString('hex')).digest('hex');
    }
    async sendMessage(nodeId, message) {
        const connection = this.activeConnections.get(nodeId);
        if (connection && connection.readyState === 1) {
            connection.send(JSON.stringify(message));
        }
    }
    evaluateCoordinatorRole() {
        // Simple coordinator election based on node ID hash
        const nodeHashes = Array.from(this.knownNodes.keys()).concat(this.nodeId)
            .map(id => ({ id, hash: crypto.createHash('sha256').update(id).digest('hex') }))
            .sort((a, b) => a.hash.localeCompare(b.hash));
        const coordinatorId = nodeHashes[0].id;
        this.isCoordinator = coordinatorId === this.nodeId;
        if (this.isCoordinator) {
            console.log('This node elected as coordinator');
            this.startCoordinatorDuties();
        }
    }
    startCoordinatorDuties() {
        // Coordinator is responsible for pattern selection and timing
        setInterval(() => {
            this.selectAndCoordinatePattern();
        }, 300000); // Every 5 minutes
    }
    selectAndCoordinatePattern() {
        if (!this.isCoordinator)
            return;
        const availablePatterns = Array.from(this.coordinationPatterns.values())
            .filter(pattern => this.knownNodes.size >= pattern.participants);
        if (availablePatterns.length === 0) {
            console.log('No patterns available for current network size');
            return;
        }
        // Select pattern based on network conditions
        const selectedPattern = this.selectOptimalPattern(availablePatterns);
        this.coordinatePattern(selectedPattern);
    }
    selectOptimalPattern(patterns) {
        // Score patterns based on network conditions
        const scores = patterns.map(pattern => ({
            pattern,
            score: this.calculatePatternScore(pattern)
        }));
        scores.sort((a, b) => b.score - a.score);
        return scores[0].pattern;
    }
    calculatePatternScore(pattern) {
        let score = 0;
        // Prefer patterns that match current network size
        const sizeRatio = this.knownNodes.size / pattern.participants;
        score += Math.min(sizeRatio, 2) * 30; // Max 60 points
        // Prefer patterns with good timing characteristics
        if (pattern.coordination.timing === 'synchronized')
            score += 20;
        if (pattern.coordination.variance < 30)
            score += 15;
        // Prefer patterns with moderate traffic volume
        if (pattern.traffic.volume >= 10 && pattern.traffic.volume <= 40)
            score += 25;
        // Add randomness to avoid predictability
        score += Math.random() * 10;
        return score;
    }
    async coordinatePattern(pattern) {
        console.log(`Coordinating pattern: ${pattern.name} via blockchain consensus`);
        this.currentPattern = pattern;
        try {
            // Propose pattern execution via blockchain
            const participants = this.selectParticipants(pattern.participants);
            const startTime = Date.now() + 60000; // Start in 60 seconds for consensus
            const blockHash = await this.blockchainCoordinator.proposePatternExecution(pattern.id, participants, startTime);
            console.log(`Pattern execution proposed in block: ${blockHash}`);
            // Also send via P2P for immediate coordination
            const coordinationMessage = {
                type: 'coordination',
                subtype: 'pattern_execution',
                nodeId: this.nodeId,
                timestamp: Date.now(),
                payload: {
                    pattern: pattern,
                    startTime: startTime,
                    participants: participants,
                    blockHash: blockHash
                }
            };
            this.p2pNetwork.broadcastMessage({
                type: 'data',
                nodeId: this.nodeId,
                timestamp: Date.now(),
                payload: coordinationMessage,
                signature: ''
            });
            // Schedule pattern execution
            setTimeout(() => {
                this.executePattern(pattern);
            }, 60000);
        }
        catch (error) {
            console.error('Failed to coordinate pattern via blockchain:', error);
            // Fallback to direct coordination
            await this.coordinatePatternDirect(pattern);
        }
    }
    async coordinatePatternDirect(pattern) {
        console.log(`Fallback: Direct coordination for pattern: ${pattern.name}`);
        const coordinationMessage = {
            type: 'coordinate',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            signature: '',
            payload: {
                pattern: pattern,
                startTime: Date.now() + 30000,
                participants: this.selectParticipants(pattern.participants)
            }
        };
        coordinationMessage.signature = this.signMessage(coordinationMessage);
        // Broadcast via P2P
        this.p2pNetwork.broadcastMessage({
            type: 'data',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: coordinationMessage,
            signature: ''
        });
        setTimeout(() => {
            this.executePattern(pattern);
        }, 30000);
    }
    selectParticipants(count) {
        const availableNodes = Array.from(this.knownNodes.keys());
        availableNodes.push(this.nodeId); // Include self
        // Shuffle and select required number of participants
        for (let i = availableNodes.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [availableNodes[i], availableNodes[j]] = [availableNodes[j], availableNodes[i]];
        }
        return availableNodes.slice(0, count);
    }
    async executePattern(pattern) {
        console.log(`Executing pattern: ${pattern.name}`);
        const startTime = Date.now();
        const endTime = startTime + pattern.duration;
        const requestInterval = 60000 / pattern.traffic.volume; // Convert to milliseconds
        const executeRequest = async () => {
            if (Date.now() >= endTime)
                return;
            // Select random target
            const target = pattern.traffic.targets[Math.floor(Math.random() * pattern.traffic.targets.length)];
            try {
                await this.sendDecoyRequest(target, pattern);
            }
            catch (error) {
                console.warn('Decoy request failed:', error);
            }
            // Schedule next request
            const variance = pattern.coordination.variance / 100;
            const delay = requestInterval * (1 + (Math.random() - 0.5) * variance);
            setTimeout(executeRequest, delay);
        };
        // Start pattern execution
        executeRequest();
        // Report completion
        setTimeout(() => {
            this.reportPatternCompletion(pattern);
        }, pattern.duration);
    }
    async sendDecoyRequest(target, pattern) {
        // Send actual HTTP request as decoy traffic
        try {
            const response = await fetch(target, {
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json, text/html, */*',
                    'X-Decoy-Pattern': pattern.id,
                    'X-Node-ID': this.nodeId
                },
                mode: 'no-cors'
            });
            console.log(`Decoy request sent to ${target} - Status: ${response.status || 'no-cors'}`);
        }
        catch (error) {
            // Ignore errors - decoy traffic should be resilient
        }
    }
    async reportPatternCompletion(pattern) {
        const reportMessage = {
            type: 'report',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            signature: '',
            payload: {
                patternId: pattern.id,
                status: 'completed',
                requestsSent: Math.floor(pattern.traffic.volume * (pattern.duration / 60000)),
                duration: pattern.duration
            }
        };
        reportMessage.signature = this.signMessage(reportMessage);
        // Send report to coordinator (if not self)
        if (!this.isCoordinator) {
            const coordinatorId = this.findCoordinator();
            if (coordinatorId) {
                await this.sendMessage(coordinatorId, reportMessage);
            }
        }
        console.log(`Pattern ${pattern.name} completed`);
    }
    findCoordinator() {
        // Find the coordinator node (lowest hash)
        const nodeHashes = Array.from(this.knownNodes.keys()).concat(this.nodeId)
            .map(id => ({ id, hash: crypto.createHash('sha256').update(id).digest('hex') }))
            .sort((a, b) => a.hash.localeCompare(b.hash));
        return nodeHashes[0]?.id || null;
    }
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, 30000); // Every 30 seconds
    }
    async sendHeartbeat() {
        const heartbeatMessage = {
            type: 'heartbeat',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            signature: '',
            payload: {
                status: 'active',
                currentPattern: this.currentPattern?.id || null,
                networkSize: this.knownNodes.size,
                uptime: process.uptime()
            }
        };
        heartbeatMessage.signature = this.signMessage(heartbeatMessage);
        // Send to all connected nodes
        for (const nodeId of this.activeConnections.keys()) {
            await this.sendMessage(nodeId, heartbeatMessage);
        }
    }
    startCoordination() {
        this.coordinationInterval = setInterval(() => {
            this.maintainNetworkTopology();
            this.cleanupStaleNodes();
        }, 60000); // Every minute
    }
    maintainNetworkTopology() {
        const activeNodes = Array.from(this.knownNodes.values())
            .filter(node => Date.now() - node.lastSeen < 120000); // Active in last 2 minutes
        this.networkTopology = {
            totalNodes: this.knownNodes.size,
            activeNodes: activeNodes.length,
            clusters: this.identifyClusters(activeNodes),
            averageLatency: this.calculateAverageLatency(),
            networkHealth: activeNodes.length / this.knownNodes.size
        };
        this.emit('topologyUpdated', this.networkTopology);
    }
    identifyClusters(nodes) {
        // Simple clustering by region
        const regionGroups = new Map();
        nodes.forEach(node => {
            const region = node.geolocation?.region || 'Unknown';
            if (!regionGroups.has(region)) {
                regionGroups.set(region, []);
            }
            regionGroups.get(region).push(node);
        });
        return Array.from(regionGroups.entries()).map(([region, regionNodes]) => ({
            id: crypto.createHash('md5').update(region).digest('hex').substring(0, 8),
            nodes: regionNodes.map(n => n.id),
            coordinator: regionNodes[0]?.id || '',
            region,
            averageLatency: 50 + Math.random() * 100, // Simulated
            capacity: regionNodes.reduce((sum, n) => sum + n.capabilities.maxBandwidth, 0)
        }));
    }
    calculateAverageLatency() {
        // Simulated average latency calculation
        return 75 + Math.random() * 50; // 75-125ms
    }
    cleanupStaleNodes() {
        const staleThreshold = Date.now() - 300000; // 5 minutes
        for (const [nodeId, node] of this.knownNodes) {
            if (node.lastSeen < staleThreshold) {
                this.knownNodes.delete(nodeId);
                this.activeConnections.delete(nodeId);
                console.log(`Removed stale node: ${nodeId}`);
            }
        }
    }
    getNetworkStatus() {
        return {
            nodeId: this.nodeId,
            isCoordinator: this.isCoordinator,
            connectedNodes: this.p2pNetwork.getPeerCount(),
            currentPattern: this.currentPattern?.name || null,
            networkHealth: this.networkTopology?.networkHealth || 0,
            totalTrafficGenerated: this.calculateTotalTraffic(),
            blockchainStatus: this.blockchainCoordinator.getNetworkState(),
            p2pStatus: this.p2pNetwork.getNetworkInfo()
        };
    }
    calculateTotalTraffic() {
        // Estimate total traffic generated (simplified)
        return Math.floor(Math.random() * 10000); // Placeholder
    }
    getNetworkTopology() {
        return this.networkTopology;
    }
    async leaveNetwork() {
        // Announce departure via blockchain
        await this.blockchainCoordinator.announceNodeLeave('shutdown');
        // Send leave message via P2P
        const leaveMessage = {
            type: 'coordination',
            subtype: 'node_leave',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: {
                reason: 'shutdown'
            }
        };
        this.p2pNetwork.broadcastMessage({
            type: 'data',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: leaveMessage,
            signature: ''
        });
        // Shutdown P2P network
        await this.p2pNetwork.shutdown();
        this.activeConnections.clear();
    }
    destroy() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        if (this.coordinationInterval) {
            clearInterval(this.coordinationInterval);
            this.coordinationInterval = null;
        }
        this.leaveNetwork().catch(console.error);
    }
}
exports.DistributedDecoyNetwork = DistributedDecoyNetwork;
//# sourceMappingURL=DistributedDecoyNetwork.js.map