export interface LogEntry {
    timestamp: string;
    level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
    category: 'STARTUP' | 'SEARCH' | 'NAVIGATION' | 'WEBVIEW' | 'NETWORK' | 'ERROR';
    message: string;
    data?: any;
}
export declare class DiagnosticLogger {
    private logFile;
    private logStream;
    private logBuffer;
    private maxBufferSize;
    constructor();
    private initializeLogFile;
    log(level: LogEntry['level'], category: LogEntry['category'], message: string, data?: any): void;
    private writeToFile;
    debug(category: LogEntry['category'], message: string, data?: any): void;
    info(category: LogEntry['category'], message: string, data?: any): void;
    warn(category: LogEntry['category'], message: string, data?: any): void;
    error(category: LogEntry['category'], message: string, data?: any): void;
    logStartup(step: string, success: boolean, details?: any): void;
    logSearchEngine(action: string, success: boolean, details?: any): void;
    logNavigation(url: string, method: string, success: boolean, details?: any): void;
    logWebview(event: string, url?: string, details?: any): void;
    logWebviewError(error: string, url?: string, details?: any): void;
    logNetwork(method: string, url: string, status?: number, details?: any): void;
    getRecentLogs(count?: number): LogEntry[];
    getLogFilePath(): string;
    close(): void;
}
export declare const logger: DiagnosticLogger;
//# sourceMappingURL=DiagnosticLogger.d.ts.map