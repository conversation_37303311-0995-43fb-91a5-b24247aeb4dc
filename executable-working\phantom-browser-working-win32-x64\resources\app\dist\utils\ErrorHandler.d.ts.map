{"version": 3, "file": "ErrorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": "AAAA,MAAM,WAAW,aAAa;IAC1B,OAAO,EAAE,KAAK,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,eAAe,CAAC,CAAC,GAAG,GAAG;IACpC,OAAO,EAAE,IAAI,CAAC;IACd,IAAI,CAAC,EAAE,CAAC,CAAC;IACT,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,SAAS,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,MAAM,WAAW,CAAC,CAAC,GAAG,GAAG,IAAI,eAAe,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC;AAEtE,oBAAY,SAAS;IAEjB,4BAA4B,iCAAiC;IAC7D,4BAA4B,iCAAiC;IAC7D,0BAA0B,+BAA+B;IAGzD,6BAA6B,kCAAkC;IAC/D,6BAA6B,kCAAkC;IAC/D,0BAA0B,+BAA+B;IAGzD,0BAA0B,+BAA+B;IACzD,0BAA0B,+BAA+B;IACzD,uBAAuB,4BAA4B;IACnD,iBAAiB,sBAAsB;IAGvC,2BAA2B,gCAAgC;IAC3D,2BAA2B,gCAAgC;IAC3D,uBAAuB,4BAA4B;IAGnD,mBAAmB,wBAAwB;IAC3C,oBAAoB,yBAAyB;IAC7C,iBAAiB,sBAAsB;IAGvC,yBAAyB,8BAA8B;IACvD,eAAe,oBAAoB;IAGnC,aAAa,kBAAkB;IAC/B,iBAAiB,sBAAsB;IACvC,aAAa,kBAAkB;CAClC;AAED,qBAAa,YAAa,SAAQ,KAAK;IACnC,SAAgB,IAAI,EAAE,SAAS,CAAC;IAChC,SAAgB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjC,SAAgB,SAAS,EAAE,MAAM,CAAC;gBAEtB,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM;IAQ9D,UAAU,IAAI,aAAa;CAS9B;AAED,qBAAa,YAAY;IACrB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAe;IAEtC,OAAO;WAEO,WAAW,IAAI,YAAY;IAOzC;;OAEG;IACI,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,aAAa;IA0B7F;;OAEG;IACI,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC;IAS/E;;OAEG;IACU,WAAW,CAAC,CAAC,EACtB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,SAAS,EAAE,SAAS,EACpB,YAAY,CAAC,EAAE,MAAM,GACtB,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAU1B;;OAEG;IACI,UAAU,CAAC,CAAC,EACf,SAAS,EAAE,MAAM,CAAC,EAClB,SAAS,EAAE,SAAS,EACpB,YAAY,CAAC,EAAE,MAAM,GACtB,WAAW,CAAC,CAAC,CAAC;IAUjB;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAkD9B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAqB7B;;OAEG;IACI,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI;IAgB5F;;OAEG;IACI,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAMjF;;OAEG;IACI,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;CAa5E;AAED,eAAe,YAAY,CAAC"}