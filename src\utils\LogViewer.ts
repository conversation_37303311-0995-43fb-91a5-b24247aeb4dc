import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';

export interface LogAnalysis {
    totalEntries: number;
    errorCount: number;
    warningCount: number;
    navigationAttempts: number;
    webviewErrors: number;
    networkErrors: number;
    searchEngineErrors: number;
    recentErrors: any[];
    commonIssues: string[];
}

export class LogViewer {
    private logsDir: string;

    constructor() {
        const userDataPath = app.getPath('userData');
        this.logsDir = path.join(userDataPath, 'logs');
    }

    public getLogFiles(): string[] {
        try {
            if (!fs.existsSync(this.logsDir)) {
                return [];
            }
            
            return fs.readdirSync(this.logsDir)
                .filter(file => file.endsWith('.log'))
                .sort((a, b) => b.localeCompare(a)); // Most recent first
        } catch (error) {
            console.error('Failed to get log files:', error);
            return [];
        }
    }

    public getLatestLogFile(): string | null {
        const files = this.getLogFiles();
        return files.length > 0 ? path.join(this.logsDir, files[0]) : null;
    }

    public readLogFile(filename: string): string {
        try {
            const filePath = path.join(this.logsDir, filename);
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            console.error('Failed to read log file:', error);
            return '';
        }
    }

    public analyzeLogFile(filename: string): LogAnalysis {
        const content = this.readLogFile(filename);
        const lines = content.split('\n').filter(line => line.trim());

        const analysis: LogAnalysis = {
            totalEntries: lines.length,
            errorCount: 0,
            warningCount: 0,
            navigationAttempts: 0,
            webviewErrors: 0,
            networkErrors: 0,
            searchEngineErrors: 0,
            recentErrors: [],
            commonIssues: []
        };

        const errorPatterns: { [key: string]: number } = {};

        lines.forEach(line => {
            // Count log levels
            if (line.includes(' ERROR ')) {
                analysis.errorCount++;
                
                // Extract recent errors
                if (analysis.recentErrors.length < 10) {
                    analysis.recentErrors.push(line);
                }

                // Track error patterns
                const errorMatch = line.match(/ERROR \[(\w+)\] (.+)/);
                if (errorMatch) {
                    const category = errorMatch[1];
                    const message = errorMatch[2];
                    const key = `${category}: ${message.split(' ')[0]}`;
                    errorPatterns[key] = (errorPatterns[key] || 0) + 1;
                }
            }
            
            if (line.includes(' WARN ')) {
                analysis.warningCount++;
            }

            // Count specific categories
            if (line.includes('[NAVIGATION]')) {
                analysis.navigationAttempts++;
            }
            
            if (line.includes('[WEBVIEW]') && line.includes(' ERROR ')) {
                analysis.webviewErrors++;
            }
            
            if (line.includes('[NETWORK]') && line.includes(' ERROR ')) {
                analysis.networkErrors++;
            }
            
            if (line.includes('[SEARCH]') && line.includes(' ERROR ')) {
                analysis.searchEngineErrors++;
            }
        });

        // Identify common issues
        analysis.commonIssues = Object.entries(errorPatterns)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([pattern, count]) => `${pattern} (${count} times)`);

        return analysis;
    }

    public generateDiagnosticReport(filename?: string): string {
        const logFile = filename || this.getLatestLogFile();
        if (!logFile) {
            return 'No log files found';
        }

        const analysis = this.analyzeLogFile(path.basename(logFile));
        const timestamp = new Date().toISOString();

        return `
# Phantom Browser Diagnostic Report
Generated: ${timestamp}
Log File: ${path.basename(logFile)}

## Summary
- Total Log Entries: ${analysis.totalEntries}
- Errors: ${analysis.errorCount}
- Warnings: ${analysis.warningCount}
- Navigation Attempts: ${analysis.navigationAttempts}

## Error Breakdown
- Webview Errors: ${analysis.webviewErrors}
- Network Errors: ${analysis.networkErrors}
- SearchEngine Errors: ${analysis.searchEngineErrors}

## Common Issues
${analysis.commonIssues.map(issue => `- ${issue}`).join('\n')}

## Recent Errors
${analysis.recentErrors.slice(0, 5).map(error => `- ${error}`).join('\n')}

## Recommendations
${this.generateRecommendations(analysis)}
`;
    }

    private generateRecommendations(analysis: LogAnalysis): string {
        const recommendations: string[] = [];

        if (analysis.webviewErrors > 0) {
            recommendations.push('- Check webview configuration and ensure proper URL formatting');
        }

        if (analysis.networkErrors > 0) {
            recommendations.push('- Verify network connectivity and proxy settings');
        }

        if (analysis.searchEngineErrors > 0) {
            recommendations.push('- Check SearchEngine initialization and provider configuration');
        }

        if (analysis.navigationAttempts === 0) {
            recommendations.push('- No navigation attempts detected - check address bar functionality');
        }

        if (analysis.errorCount > analysis.totalEntries * 0.1) {
            recommendations.push('- High error rate detected - review browser configuration');
        }

        return recommendations.length > 0 ? recommendations.join('\n') : '- No specific issues detected';
    }

    public exportDiagnosticReport(filename?: string): string {
        const report = this.generateDiagnosticReport(filename);
        const reportPath = path.join(this.logsDir, `diagnostic-report-${Date.now()}.txt`);
        
        try {
            fs.writeFileSync(reportPath, report);
            return reportPath;
        } catch (error) {
            console.error('Failed to export diagnostic report:', error);
            return '';
        }
    }

    public cleanOldLogs(daysToKeep: number = 7): number {
        try {
            const files = this.getLogFiles();
            const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
            let deletedCount = 0;

            files.forEach(file => {
                const filePath = path.join(this.logsDir, file);
                const stats = fs.statSync(filePath);
                
                if (stats.mtime.getTime() < cutoffTime) {
                    fs.unlinkSync(filePath);
                    deletedCount++;
                }
            });

            return deletedCount;
        } catch (error) {
            console.error('Failed to clean old logs:', error);
            return 0;
        }
    }
}

export const logViewer = new LogViewer();
