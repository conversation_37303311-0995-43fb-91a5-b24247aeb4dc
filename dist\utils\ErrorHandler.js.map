{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": ";;;AAiBA,IAAY,SAmCX;AAnCD,WAAY,SAAS;IACjB,iBAAiB;IACjB,0EAA6D,CAAA;IAC7D,0EAA6D,CAAA;IAC7D,sEAAyD,CAAA;IAEzD,kBAAkB;IAClB,4EAA+D,CAAA;IAC/D,4EAA+D,CAAA;IAC/D,sEAAyD,CAAA;IAEzD,eAAe;IACf,sEAAyD,CAAA;IACzD,sEAAyD,CAAA;IACzD,gEAAmD,CAAA;IACnD,oDAAuC,CAAA;IAEvC,gBAAgB;IAChB,wEAA2D,CAAA;IAC3D,wEAA2D,CAAA;IAC3D,gEAAmD,CAAA;IAEnD,iBAAiB;IACjB,wDAA2C,CAAA;IAC3C,0DAA6C,CAAA;IAC7C,oDAAuC,CAAA;IAEvC,iBAAiB;IACjB,oEAAuD,CAAA;IACvD,gDAAmC,CAAA;IAEnC,iBAAiB;IACjB,4CAA+B,CAAA;IAC/B,oDAAuC,CAAA;IACvC,4CAA+B,CAAA;AACnC,CAAC,EAnCW,SAAS,yBAAT,SAAS,QAmCpB;AAED,MAAa,YAAa,SAAQ,KAAK;IAKnC,YAAY,IAAe,EAAE,OAAe,EAAE,OAAgB;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC;IAED,UAAU;QACN,OAAO;YACH,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,IAAI,CAAC,OAAO;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;SAC5B,CAAC;IACN,CAAC;CACJ;AAtBD,oCAsBC;AAED,MAAa,YAAY;IAGrB,gBAAuB,CAAC;IAEjB,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,KAAc,EAAE,IAAgB,EAAE,OAAgB;QACzE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,KAAK,YAAY,YAAY,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YACzB,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBACvD,OAAO,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO;gBACjC,IAAI,EAAE,IAAI,IAAI,SAAS,CAAC,aAAa;gBACrC,SAAS;aACZ,CAAC;QACN,CAAC;QAED,OAAO;YACH,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;YACvD,OAAO,EAAE,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC;YACjC,IAAI,EAAE,IAAI,IAAI,SAAS,CAAC,aAAa;YACrC,SAAS;SACZ,CAAC;IACN,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAI,IAAQ,EAAE,OAAgB;QACtD,OAAO;YACH,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACpB,SAA2B,EAC3B,SAAoB,EACpB,YAAqB;QAErB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,SAAS,IAAI,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAED;;OAEG;IACI,UAAU,CACb,SAAkB,EAClB,SAAoB,EACpB,YAAqB;QAErB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,SAAS,IAAI,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,gBAAwB,EAAE,IAAgB;QACrE,wDAAwD;QACxD,MAAM,aAAa,GAA2B;YAC1C,QAAQ,EAAE,6BAA6B;YACvC,QAAQ,EAAE,mBAAmB;YAC7B,SAAS,EAAE,wBAAwB;YACnC,QAAQ,EAAE,mCAAmC;YAC7C,QAAQ,EAAE,qBAAqB;YAC/B,QAAQ,EAAE,uBAAuB;YACjC,WAAW,EAAE,qBAAqB;YAClC,cAAc,EAAE,oBAAoB;YACpC,WAAW,EAAE,2BAA2B;YACxC,cAAc,EAAE,kBAAkB;SACrC,CAAC;QAEF,2CAA2C;QAC3C,KAAK,MAAM,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YACrE,IAAI,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,OAAO,eAAe,CAAC;YAC3B,CAAC;QACL,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,YAAY,GAAuC;gBACrD,CAAC,SAAS,CAAC,4BAA4B,CAAC,EAAE,kEAAkE;gBAC5G,CAAC,SAAS,CAAC,4BAA4B,CAAC,EAAE,2DAA2D;gBACrG,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE,sDAAsD;gBAC9F,CAAC,SAAS,CAAC,6BAA6B,CAAC,EAAE,2DAA2D;gBACtG,CAAC,SAAS,CAAC,6BAA6B,CAAC,EAAE,4DAA4D;gBACvG,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,0DAA0D;gBAC/F,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,2DAA2D;gBAC1F,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,wCAAwC;gBAC7E,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,oDAAoD;gBACrF,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,+CAA+C;gBACjF,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,mDAAmD;gBAClF,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,4DAA4D;gBACnG,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,oDAAoD;gBAC/E,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,mDAAmD;aACrF,CAAC;YAEF,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrB,OAAO,YAAY,CAAC,IAAI,CAAE,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,sDAAsD;QACtD,OAAO,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAe;QACzC,0CAA0C;QAC1C,IAAI,OAAO,GAAG,OAAO;aAChB,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,gCAAgC;aAC9D,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,0BAA0B;aAClD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,uBAAuB;aAC5C,IAAI,EAAE,CAAC;QAEZ,0BAA0B;QAC1B,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,+BAA+B;QAC/B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,GAAG,CAAC;QACnB,CAAC;QAED,OAAO,OAAO,IAAI,+BAA+B,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,KAAc,EAAE,OAAe,EAAE,cAAoC;QACjF,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG;YACd,SAAS;YACT,OAAO;YACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC;gBAC5B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACrB,CAAC,CAAC,CAAC,KAAK;YACT,cAAc;SACjB,CAAC;QAEF,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS,cAAc,OAAO,GAAG,EAAE,SAAS,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,SAAkB,EAAE,OAAe,EAAE,OAAgB;QACtE,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,MAAM,IAAI,YAAY,CAAC,SAAS,CAAC,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAED;;OAEG;IACI,WAAW,CAAI,OAAmB,EAAE,SAAiB;QACxD,OAAO,OAAO,CAAC,IAAI,CAAC;YAChB,OAAO;YACP,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBAC7B,UAAU,CAAC,GAAG,EAAE;oBACZ,MAAM,CAAC,IAAI,YAAY,CACnB,SAAS,CAAC,eAAe,EACzB,6BAA6B,SAAS,IAAI,CAC7C,CAAC,CAAC;gBACP,CAAC,EAAE,SAAS,CAAC,CAAC;YAClB,CAAC,CAAC;SACL,CAAC,CAAC;IACP,CAAC;CACJ;AAhND,oCAgNC;AAED,kBAAe,YAAY,CAAC"}