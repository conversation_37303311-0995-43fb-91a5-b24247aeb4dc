/**
 * Secure Logging Utility for Phantom Browser
 * Sanitizes log messages to prevent sensitive information exposure
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}
export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: number;
    context?: string;
    sanitized: boolean;
}
export declare class SecureLogger {
    private static instance;
    private logLevel;
    private logHistory;
    private maxHistorySize;
    private constructor();
    static getInstance(): SecureLogger;
    /**
     * Set the minimum log level
     */
    setLogLevel(level: LogLevel): void;
    /**
     * Log a debug message
     */
    debug(message: string, context?: string): void;
    /**
     * Log an info message
     */
    info(message: string, context?: string): void;
    /**
     * Log a warning message
     */
    warn(message: string, context?: string): void;
    /**
     * Log an error message
     */
    error(message: string, context?: string): void;
    /**
     * Log a message with automatic sanitization
     */
    private log;
    /**
     * Sanitize log messages to remove sensitive information
     */
    private sanitizeMessage;
    /**
     * Add log entry to history with size management
     */
    private addToHistory;
    /**
     * Output log entry to console with appropriate formatting
     */
    private outputToConsole;
    /**
     * Get sanitized log history
     */
    getLogHistory(maxEntries?: number): LogEntry[];
    /**
     * Clear log history
     */
    clearHistory(): void;
    /**
     * Export logs for debugging (with additional sanitization)
     */
    exportLogs(): string;
    /**
     * Create a scoped logger for a specific context
     */
    createScopedLogger(context: string): ScopedLogger;
    /**
     * Test the sanitization with various sensitive data patterns
     */
    testSanitization(): void;
    /**
     * Check if a message contains potentially sensitive data
     */
    private containsSensitiveData;
}
/**
 * Scoped logger for specific contexts
 */
export declare class ScopedLogger {
    private logger;
    private context;
    constructor(logger: SecureLogger, context: string);
    debug(message: string): void;
    info(message: string): void;
    warn(message: string): void;
    error(message: string): void;
}
export declare const secureLogger: SecureLogger;
export default SecureLogger;
//# sourceMappingURL=SecureLogger.d.ts.map