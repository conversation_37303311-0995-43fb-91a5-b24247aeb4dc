{"version": 3, "file": "FingerprintProtection.js", "sourceRoot": "", "sources": ["../../src/privacy/FingerprintProtection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAgD;AAChD,+CAAiC;AAqBjC,MAAa,qBAAqB;IAK9B;QAFQ,qBAAgB,GAA0B,IAAI,CAAC;QAGnD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,gBAAgB;QACpB,mDAAmD;QACnD,MAAM,QAAQ,GAAG;YACb,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,mBAAmB,EAAE;SAC7B,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAChF,CAAC;IAEO,oBAAoB;QACxB,OAAO;YACH,SAAS,EAAE,iHAAiH;YAC5H,MAAM,EAAE;gBACJ,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;aACjB;YACD,QAAQ,EAAE,kBAAkB;YAC5B,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,OAAO;YACjB,mBAAmB,EAAE,CAAC;YACtB,YAAY,EAAE,CAAC;YACf,WAAW,EAAE,sBAAsB;YACnC,aAAa,EAAE,6EAA6E;YAC5F,iBAAiB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YACnD,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,EAAE;SACpD,CAAC;IACN,CAAC;IAEO,gBAAgB;QACpB,OAAO;YACH,SAAS,EAAE,uHAAuH;YAClI,MAAM,EAAE;gBACJ,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;aACjB;YACD,QAAQ,EAAE,qBAAqB;YAC/B,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,UAAU;YACpB,mBAAmB,EAAE,EAAE;YACvB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,YAAY;YACzB,aAAa,EAAE,cAAc;YAC7B,iBAAiB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YACnD,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,EAAE;SACpD,CAAC;IACN,CAAC;IAEO,kBAAkB;QACtB,OAAO;YACH,SAAS,EAAE,uGAAuG;YAClH,MAAM,EAAE;gBACJ,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;aACjB;YACD,QAAQ,EAAE,eAAe;YACzB,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,cAAc;YACxB,mBAAmB,EAAE,EAAE;YACvB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,MAAM;YACnB,aAAa,EAAE,0CAA0C;YACzD,iBAAiB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YACnD,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,EAAE;SACpD,CAAC;IACN,CAAC;IAEO,mBAAmB;QACvB,OAAO;YACH,SAAS,EAAE,yIAAyI;YACpJ,MAAM,EAAE;gBACJ,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;aACjB;YACD,QAAQ,EAAE,kBAAkB;YAC5B,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,QAAQ;YAClB,mBAAmB,EAAE,CAAC;YACtB,YAAY,EAAE,CAAC;YACf,WAAW,EAAE,YAAY;YACzB,aAAa,EAAE,eAAe;YAC9B,iBAAiB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YACnD,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,EAAE;SACpD,CAAC;IACN,CAAC;IAEO,yBAAyB;QAC7B,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;IAEO,wBAAwB;QAC5B,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;IAEO,qBAAqB;QACzB,MAAM,YAAY,GAAG;YACjB,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,kBAAkB,EAAE;SAC5B,CAAC;QAEF,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAE3E,yBAAyB;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC3D,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;QAC3D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,0BAA0B;QAC9B,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,sDAAsD;QACtD,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC7E,IAAI,OAAO,CAAC,eAAe;gBACvB,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAEtE,8CAA8C;gBAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBACzD,OAAO,CAAC,eAAe,CAAC,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC7E,CAAC;YAED,QAAQ,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IAEO,wBAAwB;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;QAEpC,OAAO;;;;;;;sCAOuB,OAAO,CAAC,MAAM,CAAC,KAAK;uCACnB,OAAO,CAAC,MAAM,CAAC,MAAM;2CACjB,OAAO,CAAC,MAAM,CAAC,KAAK;4CACnB,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE;2CAC3B,OAAO,CAAC,MAAM,CAAC,UAAU;2CACzB,OAAO,CAAC,MAAM,CAAC,UAAU;;;;;0CAK1B,OAAO,CAAC,QAAQ;0CAChB,OAAO,CAAC,QAAQ;4CACd,OAAO,CAAC,QAAQ;oDACR,OAAO,CAAC,mBAAmB;6CAClC,OAAO,CAAC,YAAY;2CACtB,OAAO,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA6B1B,OAAO,CAAC,WAAW;;;kCAGnB,OAAO,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAyCZ,OAAO,CAAC,QAAQ;6BAC9B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;SA0B5D,CAAC;IACN,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACtC,yCAAyC;QACzC,MAAM,OAAO,GAA8B;YACvC,kBAAkB,EAAE,GAAG;YACvB,qBAAqB,EAAE,GAAG;YAC1B,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,GAAG;SACrB,CAAC;QACF,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEO,oBAAoB;QACxB,8CAA8C;QAC9C,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACvB,CAAC;IAEO,aAAa;QACjB,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;QAEjC,4BAA4B;QAC5B,kBAAO,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAE1D,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC/C,CAAC;IAED,iBAAiB;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IAED,UAAU,CAAC,OAA2B;QAClC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,kBAAO,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;IAED,OAAO;QACH,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;IACL,CAAC;CACJ;AA1UD,sDA0UC"}