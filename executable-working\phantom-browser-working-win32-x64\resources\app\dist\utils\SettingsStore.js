"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsStore = void 0;
const electron_store_1 = __importDefault(require("electron-store"));
class SettingsStore {
    constructor() {
        this.store = new electron_store_1.default({
            name: 'phantom-browser-settings',
            defaults: this.getDefaultSettings(),
            schema: {
                privacy: {
                    type: 'object',
                    properties: {
                        blockTrackers: { type: 'boolean' },
                        blockAds: { type: 'boolean' },
                        blockFingerprinting: { type: 'boolean' },
                        spoofTimezone: { type: 'boolean' },
                        spoofLanguage: { type: 'boolean' },
                        spoofScreen: { type: 'boolean' },
                        randomizeCanvasFingerprint: { type: 'boolean' },
                        blockWebRTC: { type: 'boolean' },
                        clearCookiesOnExit: { type: 'boolean' },
                        useDoH: { type: 'boolean' }
                    }
                },
                security: {
                    type: 'object',
                    properties: {
                        enableSandbox: { type: 'boolean' },
                        blockDangerousDownloads: { type: 'boolean' },
                        enableCSP: { type: 'boolean' },
                        blockMixedContent: { type: 'boolean' },
                        enableHSTS: { type: 'boolean' },
                        blockPlugins: { type: 'boolean' },
                        enableMemoryProtection: { type: 'boolean' },
                        clearDataOnExit: { type: 'boolean' }
                    }
                },
                proxy: {
                    type: 'object',
                    properties: {
                        currentProxy: { type: ['object', 'null'] },
                        proxyList: { type: 'array' },
                        rotationEnabled: { type: 'boolean' },
                        rotationInterval: { type: 'number' }
                    }
                },
                search: {
                    type: 'object',
                    properties: {
                        defaultProvider: { type: 'string' },
                        enableSuggestions: { type: 'boolean' },
                        enableHistory: { type: 'boolean' },
                        maxSuggestions: { type: 'number' },
                        privacyMode: { type: 'string', enum: ['strict', 'balanced', 'standard'] },
                        customProviders: { type: 'array' }
                    }
                },
                steganographic: {
                    type: 'object',
                    properties: {
                        enableTrafficObfuscation: { type: 'boolean' },
                        enableTimingRandomization: { type: 'boolean' },
                        enableBehaviorMasking: { type: 'boolean' },
                        enableDecoyTraffic: { type: 'boolean' },
                        obfuscationIntensity: { type: 'string', enum: ['low', 'medium', 'high', 'maximum'] },
                        enableDPIEvasion: { type: 'boolean' },
                        enableQuantumResistance: { type: 'boolean' },
                        enableCrossPlatformCoordination: { type: 'boolean' }
                    }
                }
            }
        });
    }
    static getInstance() {
        if (!SettingsStore.instance) {
            SettingsStore.instance = new SettingsStore();
        }
        return SettingsStore.instance;
    }
    getDefaultSettings() {
        return {
            privacy: {
                blockTrackers: true,
                blockAds: true,
                blockFingerprinting: true,
                spoofTimezone: true,
                spoofLanguage: true,
                spoofScreen: true,
                randomizeCanvasFingerprint: true,
                blockWebRTC: true,
                clearCookiesOnExit: true,
                useDoH: true
            },
            security: {
                enableSandbox: true,
                blockDangerousDownloads: true,
                enableCSP: true,
                blockMixedContent: true,
                enableHSTS: true,
                blockPlugins: true,
                enableMemoryProtection: true,
                clearDataOnExit: true
            },
            proxy: {
                currentProxy: null,
                proxyList: [
                    {
                        type: 'direct',
                        host: '',
                        port: 0,
                        enabled: true,
                        name: 'Direct Connection',
                        description: 'No proxy - direct internet connection'
                    }
                ],
                rotationEnabled: false,
                rotationInterval: 10
            },
            search: {
                defaultProvider: 'duckduckgo',
                enableSuggestions: true,
                enableHistory: true,
                maxSuggestions: 8,
                privacyMode: 'balanced',
                customProviders: []
            },
            steganographic: {
                enableTrafficObfuscation: true,
                enableTimingRandomization: true,
                enableBehaviorMasking: true,
                enableDecoyTraffic: true,
                obfuscationIntensity: 'high',
                enableDPIEvasion: true,
                enableQuantumResistance: true,
                enableCrossPlatformCoordination: true
            }
        };
    }
    // Generic methods for accessing settings
    get(key) {
        try {
            return this.store.get(key);
        }
        catch (error) {
            console.error(`Failed to get setting ${key}:`, error);
            return this.getDefaultSettings()[key];
        }
    }
    set(key, value) {
        try {
            this.store.set(key, value);
            console.log(`Settings saved for ${key}`);
        }
        catch (error) {
            console.error(`Failed to save setting ${key}:`, error);
            throw new Error(`Failed to save ${key} settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    has(key) {
        return this.store.has(key);
    }
    delete(key) {
        try {
            this.store.delete(key);
            console.log(`Settings deleted for ${key}`);
        }
        catch (error) {
            console.error(`Failed to delete setting ${key}:`, error);
            throw new Error(`Failed to delete ${key} settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    clear() {
        try {
            this.store.clear();
            console.log('All settings cleared');
        }
        catch (error) {
            console.error('Failed to clear settings:', error);
            throw new Error(`Failed to clear settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    getStorePath() {
        return this.store.path;
    }
    reset() {
        try {
            this.store.store = this.getDefaultSettings();
            console.log('Settings reset to defaults');
        }
        catch (error) {
            console.error('Failed to reset settings:', error);
            throw new Error(`Failed to reset settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    // Specific helper methods for common operations
    updatePrivacySettings(settings) {
        const currentSettings = this.get('privacy');
        this.set('privacy', { ...currentSettings, ...settings });
    }
    updateSecuritySettings(settings) {
        const currentSettings = this.get('security');
        this.set('security', { ...currentSettings, ...settings });
    }
    updateProxySettings(settings) {
        const currentSettings = this.get('proxy');
        this.set('proxy', { ...currentSettings, ...settings });
    }
    updateSearchSettings(settings) {
        const currentSettings = this.get('search');
        this.set('search', { ...currentSettings, ...settings });
    }
    updateSteganographicSettings(settings) {
        const currentSettings = this.get('steganographic');
        this.set('steganographic', { ...currentSettings, ...settings });
    }
}
exports.SettingsStore = SettingsStore;
exports.default = SettingsStore;
//# sourceMappingURL=SettingsStore.js.map