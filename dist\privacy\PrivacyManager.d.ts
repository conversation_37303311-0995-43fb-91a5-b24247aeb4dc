import { ApiResponse } from '../utils/ErrorHandler';
export interface PrivacySettings {
    blockTrackers: boolean;
    blockAds: boolean;
    blockFingerprinting: boolean;
    spoofTimezone: boolean;
    spoofLanguage: boolean;
    spoofScreen: boolean;
    randomizeCanvasFingerprint: boolean;
    blockWebRTC: boolean;
    clearCookiesOnExit: boolean;
    useDoH: boolean;
}
export declare class PrivacyManager {
    private settings;
    private trackingDomains;
    private adBlockRules;
    private settingsStore;
    private errorHandler;
    constructor();
    initialize(): Promise<void>;
    private loadBlockLists;
    private configureSession;
    private setupRequestInterception;
    private setupScriptInjection;
    private isTrackingDomain;
    private matchesAdBlockRule;
    private spoofReferrer;
    private generateRandomUserAgent;
    private generatePACScript;
    private generateProtectionScript;
    private loadSettings;
    private saveSettings;
    updateSettings(newSettings: Partial<PrivacySettings>): ApiResponse<void>;
    getSettings(): PrivacySettings;
    clearPrivacyData(): Promise<ApiResponse<void>>;
}
//# sourceMappingURL=PrivacyManager.d.ts.map