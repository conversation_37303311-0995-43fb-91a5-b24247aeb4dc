"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyManager = void 0;
const electron_1 = require("electron");
const SettingsStore_1 = require("../utils/SettingsStore");
const ErrorHandler_1 = require("../utils/ErrorHandler");
class ProxyManager {
    constructor() {
        this.currentProxy = null;
        this.proxyList = [];
        this.rotationEnabled = false;
        this.rotationInterval = null;
        this.vpnConfig = null;
        this.settingsStore = SettingsStore_1.SettingsStore.getInstance();
        this.errorHandler = ErrorHandler_1.ErrorHandler.getInstance();
        this.loadSettings();
    }
    async initialize() {
        // Initialization is now handled in constructor via loadSettings
        console.log('ProxyManager initialized');
    }
    loadSettings() {
        try {
            const proxySettings = this.settingsStore.get('proxy');
            this.currentProxy = proxySettings.currentProxy;
            this.proxyList = proxySettings.proxyList;
            this.rotationEnabled = proxySettings.rotationEnabled;
            console.log('Proxy settings loaded from storage');
        }
        catch (error) {
            console.error('Failed to load proxy settings:', error);
            // Use default settings if loading fails
            this.initializeDefaultProxies();
        }
    }
    saveSettings() {
        try {
            this.settingsStore.updateProxySettings({
                currentProxy: this.currentProxy,
                proxyList: this.proxyList,
                rotationEnabled: this.rotationEnabled,
                rotationInterval: 10 // Default interval
            });
            console.log('Proxy settings saved to storage');
        }
        catch (error) {
            console.error('Failed to save proxy settings:', error);
            throw new Error(`Failed to save proxy settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    initializeDefaultProxies() {
        // Initialize with safe default proxies
        this.proxyList = [
            {
                type: 'direct',
                host: '',
                port: 0,
                enabled: true,
                name: 'Direct Connection',
                description: 'No proxy - direct internet connection'
            },
            // Add Tor proxy if available (common default)
            {
                type: 'socks5',
                host: '127.0.0.1',
                port: 9050,
                enabled: false,
                name: 'Tor (Local)',
                description: 'Local Tor SOCKS5 proxy'
            }
        ];
        this.currentProxy = null;
        this.rotationEnabled = false;
    }
    async setProxy(config) {
        this.currentProxy = config;
        if (config.type === 'direct') {
            await this.clearProxy();
            return;
        }
        const ses = electron_1.session.defaultSession;
        try {
            let proxyRules = '';
            switch (config.type) {
                case 'http':
                case 'https':
                    proxyRules = `${config.type}=${config.host}:${config.port}`;
                    break;
                case 'socks4':
                case 'socks5':
                    proxyRules = `${config.type}=${config.host}:${config.port}`;
                    break;
            }
            await ses.setProxy({
                proxyRules,
                proxyBypassRules: 'localhost,127.0.0.1,<local>'
            });
            // Handle proxy authentication if credentials are provided
            if (config.username && config.password) {
                this.setupProxyAuthentication(config.username, config.password);
                console.log(`Proxy set with authentication: ${config.type}://${config.host}:${config.port}`);
            }
            else {
                console.log(`Proxy set: ${config.type}://${config.host}:${config.port}`);
            }
            // Save settings after successful proxy change
            this.saveSettings();
        }
        catch (error) {
            console.error('Failed to set proxy:', error);
            throw error;
        }
    }
    async clearProxy() {
        const ses = electron_1.session.defaultSession;
        await ses.setProxy({ proxyRules: 'direct://' });
        this.currentProxy = null;
        // Remove any existing authentication handlers
        electron_1.app.removeAllListeners('login');
        // Save settings after clearing proxy
        this.saveSettings();
        console.log('Proxy cleared');
    }
    setupProxyAuthentication(username, password) {
        // Remove any existing login handlers to avoid duplicates
        electron_1.app.removeAllListeners('login');
        // Set up authentication handler for proxy
        electron_1.app.on('login', (event, webContents, authenticationResponseDetails, authInfo, callback) => {
            // Check if this is a proxy authentication request
            if (authInfo.isProxy) {
                event.preventDefault();
                console.log('Providing proxy authentication credentials');
                callback(username, password);
            }
            else {
                // For non-proxy authentication, let the default behavior handle it
                callback('', '');
            }
        });
        console.log('Proxy authentication handler configured');
    }
    async testProxy(config) {
        try {
            // Test proxy connectivity
            if (config.type === 'socks5' || config.type === 'socks4') {
                return await this.testSocksProxy(config);
            }
            else {
                return await this.testHttpProxy(config);
            }
        }
        catch (error) {
            console.error(`Proxy test failed for ${config.host}:${config.port}:`, error);
            return false;
        }
    }
    async testSocksProxy(config) {
        try {
            // Simplified SOCKS proxy test
            return new Promise((resolve) => {
                const net = require('net');
                const socket = net.createConnection(config.port, config.host);
                socket.on('connect', () => {
                    socket.destroy();
                    resolve(true);
                });
                socket.on('error', () => {
                    resolve(false);
                });
                socket.setTimeout(5000, () => {
                    socket.destroy();
                    resolve(false);
                });
            });
        }
        catch {
            return false;
        }
    }
    async testHttpProxy(config) {
        // Simplified HTTP proxy test
        // In a real implementation, you'd make an actual HTTP request through the proxy
        return new Promise((resolve) => {
            const net = require('net');
            const socket = net.createConnection(config.port, config.host);
            socket.on('connect', () => {
                socket.destroy();
                resolve(true);
            });
            socket.on('error', () => {
                resolve(false);
            });
            socket.setTimeout(5000, () => {
                socket.destroy();
                resolve(false);
            });
        });
    }
    async rotateProxy() {
        const workingProxies = [];
        // Test all proxies and find working ones
        for (const proxy of this.proxyList) {
            if (proxy.enabled && await this.testProxy(proxy)) {
                workingProxies.push(proxy);
            }
        }
        if (workingProxies.length === 0) {
            console.warn('No working proxies found, using direct connection');
            await this.clearProxy();
            return;
        }
        // Select a random working proxy
        const randomProxy = workingProxies[Math.floor(Math.random() * workingProxies.length)];
        if (randomProxy) {
            await this.setProxy(randomProxy);
        }
    }
    enableProxyRotation(intervalMinutes = 10) {
        this.rotationEnabled = true;
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
        }
        this.rotationInterval = setInterval(async () => {
            await this.rotateProxy();
        }, intervalMinutes * 60 * 1000);
        console.log(`Proxy rotation enabled (${intervalMinutes} minutes interval)`);
    }
    disableProxyRotation() {
        this.rotationEnabled = false;
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }
        console.log('Proxy rotation disabled');
    }
    validateProxyConfig(config) {
        try {
            // Validate proxy type
            this.errorHandler.validateInput(['http', 'https', 'socks4', 'socks5', 'direct'].includes(config.type), 'Invalid proxy type. Must be one of: http, https, socks4, socks5, direct');
            // Validate host for non-direct connections
            if (config.type !== 'direct') {
                this.errorHandler.validateInput(!!(config.host && config.host.trim().length > 0), 'Proxy host is required for non-direct connections');
                this.errorHandler.validateInput(config.port > 0 && config.port <= 65535, 'Proxy port must be between 1 and 65535');
                // Validate host format (basic check)
                const hostRegex = /^[a-zA-Z0-9.-]+$/;
                this.errorHandler.validateInput(hostRegex.test(config.host), 'Invalid host format. Only alphanumeric characters, dots, and hyphens are allowed');
            }
            // Validate authentication if provided
            if (config.username || config.password) {
                this.errorHandler.validateInput(!!(config.username && config.username.trim().length > 0), 'Username is required when password is provided');
                this.errorHandler.validateInput(!!(config.password && config.password.trim().length > 0), 'Password is required when username is provided');
            }
            return this.errorHandler.createSuccessResponse(undefined, 'Proxy configuration is valid');
        }
        catch (error) {
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.PROXY_SETTINGS_SAVE_FAILED);
        }
    }
    async addProxy(config) {
        try {
            // Validate the proxy configuration
            const validationResult = this.validateProxyConfig(config);
            if (!validationResult.success) {
                return validationResult;
            }
            // Check for duplicates
            const isDuplicate = this.proxyList.some(p => p.host === config.host &&
                p.port === config.port &&
                p.type === config.type);
            if (isDuplicate) {
                return this.errorHandler.createErrorResponse(new Error('Proxy already exists in the list'), ErrorHandler_1.ErrorCode.PROXY_SETTINGS_SAVE_FAILED);
            }
            // Test the proxy if it's not a direct connection
            if (config.type !== 'direct') {
                const isWorking = await this.testProxy(config);
                if (!isWorking) {
                    return this.errorHandler.createErrorResponse(new Error('Proxy connection test failed. The proxy may not be working.'), ErrorHandler_1.ErrorCode.PROXY_CONNECTION_FAILED);
                }
            }
            // Add the proxy to the list
            this.proxyList.push({
                ...config,
                enabled: config.enabled !== undefined ? config.enabled : true,
                name: config.name || `${config.type.toUpperCase()} ${config.host}:${config.port}`,
                description: config.description || `${config.type.toUpperCase()} proxy server`
            });
            this.saveSettings();
            return this.errorHandler.createSuccessResponse(undefined, 'Proxy added successfully');
        }
        catch (error) {
            this.errorHandler.logError(error, 'ProxyManager.addProxy', { config });
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.PROXY_SETTINGS_SAVE_FAILED);
        }
    }
    removeProxy(host, port) {
        try {
            const initialLength = this.proxyList.length;
            this.proxyList = this.proxyList.filter(p => !(p.host === host && p.port === port));
            if (this.proxyList.length === initialLength) {
                return this.errorHandler.createErrorResponse(new Error('Proxy not found in the list'), ErrorHandler_1.ErrorCode.PROXY_SETTINGS_SAVE_FAILED);
            }
            // If the removed proxy was the current one, clear it
            if (this.currentProxy && this.currentProxy.host === host && this.currentProxy.port === port) {
                this.currentProxy = null;
            }
            this.saveSettings();
            return this.errorHandler.createSuccessResponse(undefined, 'Proxy removed successfully');
        }
        catch (error) {
            this.errorHandler.logError(error, 'ProxyManager.removeProxy', { host, port });
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.PROXY_SETTINGS_SAVE_FAILED);
        }
    }
    async updateProxy(oldHost, oldPort, newConfig) {
        try {
            // Validate the new configuration
            const validationResult = this.validateProxyConfig(newConfig);
            if (!validationResult.success) {
                return validationResult;
            }
            // Find the proxy to update
            const proxyIndex = this.proxyList.findIndex(p => p.host === oldHost && p.port === oldPort);
            if (proxyIndex === -1) {
                return this.errorHandler.createErrorResponse(new Error('Proxy not found in the list'), ErrorHandler_1.ErrorCode.PROXY_SETTINGS_SAVE_FAILED);
            }
            // Test the new proxy configuration if it's not a direct connection
            if (newConfig.type !== 'direct') {
                const isWorking = await this.testProxy(newConfig);
                if (!isWorking) {
                    return this.errorHandler.createErrorResponse(new Error('Updated proxy configuration test failed'), ErrorHandler_1.ErrorCode.PROXY_CONNECTION_FAILED);
                }
            }
            // Update the proxy
            this.proxyList[proxyIndex] = {
                ...newConfig,
                enabled: newConfig.enabled !== undefined ? newConfig.enabled : true,
                name: newConfig.name || `${newConfig.type.toUpperCase()} ${newConfig.host}:${newConfig.port}`,
                description: newConfig.description || `${newConfig.type.toUpperCase()} proxy server`
            };
            this.saveSettings();
            return this.errorHandler.createSuccessResponse(undefined, 'Proxy updated successfully');
        }
        catch (error) {
            this.errorHandler.logError(error, 'ProxyManager.updateProxy', { oldHost, oldPort, newConfig });
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.PROXY_SETTINGS_SAVE_FAILED);
        }
    }
    getProxyList() {
        return [...this.proxyList];
    }
    getCurrentProxy() {
        return this.currentProxy ? { ...this.currentProxy } : null;
    }
    async setupDNSOverHTTPS() {
        const ses = electron_1.session.defaultSession;
        // Configure DNS over HTTPS
        await ses.setProxy({
            mode: 'pac_script',
            pacScript: `
                function FindProxyForURL(url, host) {
                    // Use DNS over HTTPS providers
                    if (isInNet(dnsResolve(host), "0.0.0.0", "0.0.0.0")) {
                        return "HTTPS *******:443; HTTPS *******:443; DIRECT";
                    }
                    return "DIRECT";
                }
            `
        });
    }
    async setupTrafficObfuscation() {
        const ses = electron_1.session.defaultSession;
        // Add random delays to requests to obfuscate traffic patterns
        ses.webRequest.onBeforeRequest({ urls: ['<all_urls>'] }, (details, callback) => {
            const delay = Math.random() * 100; // Random delay up to 100ms
            setTimeout(() => {
                callback({ cancel: false });
            }, delay);
        });
        // Add random headers to obfuscate requests
        ses.webRequest.onBeforeSendHeaders({ urls: ['<all_urls>'] }, (details, callback) => {
            const headers = details.requestHeaders;
            // Add random cache control
            headers['Cache-Control'] = Math.random() > 0.5 ? 'no-cache' : 'max-age=0';
            // Add random connection header
            headers['Connection'] = Math.random() > 0.5 ? 'keep-alive' : 'close';
            callback({ requestHeaders: headers });
        });
    }
    // VPN Integration (simplified)
    async connectVPN(config) {
        this.vpnConfig = config;
        // In a real implementation, this would integrate with VPN clients
        // For now, we'll simulate VPN connection by setting up a proxy
        console.log(`VPN connection simulated: ${config.provider} - ${config.server}`);
    }
    async disconnectVPN() {
        this.vpnConfig = null;
        await this.clearProxy();
        console.log('VPN disconnected');
    }
    getVPNStatus() {
        return {
            connected: this.vpnConfig !== null,
            config: this.vpnConfig || undefined
        };
    }
    destroy() {
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }
    }
}
exports.ProxyManager = ProxyManager;
//# sourceMappingURL=ProxyManager.js.map