<!DOCTYPE html>
<html>
<head>
    <title>Phantom Browser Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #testResults {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🚀 Phantom Browser Production Test</h1>
    
    <div class="test-container">
        <h2>📋 Test Controls</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="testAPI()">Test API</button>
        <button onclick="testNetworkConfig()">Test Network Config</button>
        <button onclick="testNavigation()">Test Navigation</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script>
        let testCount = 0;

        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `${++testCount}. ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            testCount = 0;
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 Starting comprehensive test suite...', 'info');
            
            await testAPI();
            await testNetworkConfig();
            await testNavigation();
            await testUIElements();
            
            addResult('✅ All tests completed!', 'pass');
        }

        async function testAPI() {
            addResult('🔌 Testing API Availability...', 'info');
            
            // Test if phantomAPI is available
            if (typeof window.phantomAPI !== 'undefined') {
                addResult('✅ phantomAPI is available', 'pass');
                
                // Test individual methods
                const methods = [
                    'navigate', 'goBack', 'goForward', 'reload',
                    'switchToProxy', 'switchToDoH', 'switchToDirect',
                    'getNetworkStatus', 'getNetworkConfig',
                    'getPrivacySettings', 'updatePrivacySettings'
                ];
                
                methods.forEach(method => {
                    if (typeof window.phantomAPI[method] === 'function') {
                        addResult(`✅ ${method} method available`, 'pass');
                    } else {
                        addResult(`❌ ${method} method missing`, 'fail');
                    }
                });
            } else {
                addResult('❌ phantomAPI is not available', 'fail');
            }
        }

        async function testNetworkConfig() {
            addResult('🌐 Testing Network Configuration...', 'info');
            
            if (window.phantomAPI) {
                try {
                    // Test getting network status
                    if (typeof window.phantomAPI.getNetworkStatus === 'function') {
                        addResult('✅ getNetworkStatus method exists', 'pass');
                        
                        // Try to call it
                        try {
                            const status = await window.phantomAPI.getNetworkStatus();
                            addResult('✅ getNetworkStatus call successful', 'pass');
                        } catch (error) {
                            addResult(`⚠️ getNetworkStatus call failed: ${error.message}`, 'fail');
                        }
                    }
                    
                    // Test network configuration methods
                    const networkMethods = ['switchToDirect', 'switchToProxy', 'switchToDoH'];
                    networkMethods.forEach(method => {
                        if (typeof window.phantomAPI[method] === 'function') {
                            addResult(`✅ ${method} method available`, 'pass');
                        } else {
                            addResult(`❌ ${method} method missing - THIS WAS THE ORIGINAL BUG!`, 'fail');
                        }
                    });
                    
                } catch (error) {
                    addResult(`❌ Network configuration test failed: ${error.message}`, 'fail');
                }
            } else {
                addResult('❌ Cannot test network config - phantomAPI not available', 'fail');
            }
        }

        async function testNavigation() {
            addResult('🌍 Testing Navigation Functionality...', 'info');
            
            // Check if address bar exists
            const addressBar = document.getElementById('addressBar');
            if (addressBar) {
                addResult('✅ Address bar element found', 'pass');
                
                // Check if webview exists
                const webview = document.getElementById('webview');
                if (webview) {
                    addResult('✅ Webview element found', 'pass');
                    
                    // Test navigation function
                    if (window.phantomBrowserUI && typeof window.phantomBrowserUI.navigate === 'function') {
                        addResult('✅ Navigation function available', 'pass');
                        
                        // Test URL processing
                        try {
                            // Simulate typing in address bar
                            addressBar.value = 'https://example.com';
                            addResult('✅ Address bar accepts input', 'pass');
                            
                            // Test if we can trigger navigation (without actually navigating)
                            addResult('✅ Navigation system ready for testing', 'pass');
                            addResult('📝 Manual test: Type a URL and press Enter to verify navigation', 'info');
                            
                        } catch (error) {
                            addResult(`❌ Navigation test failed: ${error.message}`, 'fail');
                        }
                    } else {
                        addResult('❌ Navigation function not available', 'fail');
                    }
                } else {
                    addResult('❌ Webview element not found', 'fail');
                }
            } else {
                addResult('❌ Address bar element not found', 'fail');
            }
        }

        async function testUIElements() {
            addResult('🎨 Testing UI Elements...', 'info');
            
            // Test essential UI elements
            const elements = [
                { id: 'addressBar', name: 'Address Bar' },
                { id: 'webview', name: 'Web View' },
                { id: 'newTabBtn', name: 'New Tab Button' },
                { id: 'backBtn', name: 'Back Button' },
                { id: 'forwardBtn', name: 'Forward Button' },
                { id: 'reloadBtn', name: 'Reload Button' },
                { id: 'privacyPanelBtn', name: 'Privacy Panel Button' }
            ];
            
            elements.forEach(({ id, name }) => {
                const element = document.getElementById(id);
                if (element) {
                    addResult(`✅ ${name} found`, 'pass');
                } else {
                    addResult(`❌ ${name} missing`, 'fail');
                }
            });
            
            // Test if privacy panel can be opened
            const privacyBtn = document.getElementById('privacyPanelBtn');
            if (privacyBtn) {
                addResult('📝 Manual test: Click privacy button to test network configuration dialog', 'info');
            }
        }

        // Auto-run basic tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🔄 Auto-running basic API test...', 'info');
                testAPI();
            }, 1000);
        });
    </script>
</body>
</html>
