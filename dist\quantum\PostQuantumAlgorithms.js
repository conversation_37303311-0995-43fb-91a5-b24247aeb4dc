"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostQuantumAlgorithms = void 0;
const crypto = __importStar(require("crypto"));
class PostQuantumAlgorithms {
    // Kyber (Lattice-based Key Encapsulation Mechanism)
    static generateKyberKeyPair(securityLevel) {
        console.log(`Generating Kyber key pair (security level ${securityLevel})`);
        const dimension = this.KYBER_DIMENSIONS[securityLevel];
        const modulus = this.KYBER_MODULUS;
        // Generate secret vector s with small coefficients
        const secretVector = this.generateSmallVector(dimension);
        // Generate error vector e with small coefficients
        const errorVector = this.generateSmallVector(dimension);
        // Generate random polynomial matrix A
        const polynomialMatrix = this.generateRandomMatrix(dimension, dimension, modulus);
        // Compute public key: t = A*s + e (mod q)
        const publicErrorVector = this.matrixVectorMultiply(polynomialMatrix, secretVector, modulus);
        for (let i = 0; i < publicErrorVector.length; i++) {
            publicErrorVector[i] = (publicErrorVector[i] + errorVector[i]) % modulus;
        }
        return {
            publicKey: {
                polynomialMatrix,
                errorVector: publicErrorVector,
                modulus
            },
            privateKey: {
                secretVector,
                errorVector
            }
        };
    }
    static kyberEncapsulate(publicKey, message) {
        console.log('Performing Kyber encapsulation');
        const dimension = publicKey.polynomialMatrix.length;
        const modulus = publicKey.modulus;
        // Generate random vector r with small coefficients
        const randomVector = this.generateSmallVector(dimension);
        // Generate small error vectors
        const errorVector1 = this.generateSmallVector(dimension);
        const errorVector2 = this.generateSmallVector(1)[0];
        // Compute u = A^T * r + e1
        const transposeMatrix = this.transposeMatrix(publicKey.polynomialMatrix);
        const u = this.matrixVectorMultiply(transposeMatrix, randomVector, modulus);
        for (let i = 0; i < u.length; i++) {
            u[i] = (u[i] + errorVector1[i]) % modulus;
        }
        // Compute v = t^T * r + e2 + encode(message)
        const tDotR = this.vectorDotProduct(publicKey.errorVector, randomVector, modulus);
        const encodedMessage = this.encodeMessage(message, modulus);
        const v = (tDotR + errorVector2 + encodedMessage) % modulus;
        // Create ciphertext
        const ciphertext = Buffer.concat([
            Buffer.from(u.map(x => x & 0xFF)),
            Buffer.from([v & 0xFF])
        ]);
        // Derive shared secret from randomness
        const sharedSecret = crypto.createHash('sha3-256')
            .update(Buffer.from(randomVector.map(x => x & 0xFF)))
            .digest();
        return { ciphertext, sharedSecret };
    }
    static kyberDecapsulate(privateKey, ciphertext) {
        console.log('Performing Kyber decapsulation');
        // Parse ciphertext
        const dimension = privateKey.secretVector.length;
        const u = Array.from(ciphertext.slice(0, dimension)).map(x => x);
        const v = ciphertext[dimension];
        // Compute s^T * u
        const sDotU = this.vectorDotProduct(privateKey.secretVector, u, this.KYBER_MODULUS);
        // Recover message: m = v - s^T * u
        const recoveredMessage = (v - sDotU + this.KYBER_MODULUS) % this.KYBER_MODULUS;
        // Derive shared secret (simplified)
        return crypto.createHash('sha3-256')
            .update(Buffer.from([recoveredMessage]))
            .digest();
    }
    // Dilithium (Lattice-based Digital Signatures)
    static generateDilithiumKeyPair(securityLevel) {
        console.log(`Generating Dilithium key pair (security level ${securityLevel})`);
        const dimension = securityLevel * 256;
        const modulus = this.DILITHIUM_MODULUS;
        // Generate secret vectors s1, s2 with small coefficients
        const s1 = this.generateSmallVector(dimension);
        const s2 = this.generateSmallVector(dimension);
        // Generate random matrix A
        const matrixA = this.generateRandomMatrix(dimension, dimension, modulus);
        // Compute t = A*s1 + s2
        const t = this.matrixVectorMultiply(matrixA, s1, modulus);
        for (let i = 0; i < t.length; i++) {
            t[i] = (t[i] + s2[i]) % modulus;
        }
        // Pack keys
        const publicKey = Buffer.concat([
            Buffer.from(t.map(x => x & 0xFF)),
            this.serializeMatrix(matrixA)
        ]);
        const privateKey = Buffer.concat([
            Buffer.from(s1.map(x => x & 0xFF)),
            Buffer.from(s2.map(x => x & 0xFF))
        ]);
        return { publicKey, privateKey };
    }
    static dilithiumSign(message, privateKey) {
        console.log('Creating Dilithium signature');
        // Hash message
        const messageHash = crypto.createHash('sha3-512').update(message).digest();
        // Generate random nonce
        const nonce = crypto.randomBytes(32);
        // Create commitment (simplified)
        const commitment = crypto.createHash('sha3-256')
            .update(messageHash)
            .update(nonce)
            .digest();
        // Create challenge
        const challenge = crypto.createHash('sha3-256')
            .update(commitment)
            .update(messageHash)
            .digest();
        // Create response (simplified)
        const response = crypto.createHash('sha3-512')
            .update(privateKey)
            .update(challenge)
            .update(nonce)
            .digest();
        // Combine signature components
        const signature = Buffer.concat([commitment, challenge, response]);
        return {
            signature,
            challenge,
            response,
            commitment
        };
    }
    static dilithiumVerify(message, signature, publicKey) {
        console.log('Verifying Dilithium signature');
        // Hash message
        const messageHash = crypto.createHash('sha3-512').update(message).digest();
        // Recompute challenge
        const recomputedChallenge = crypto.createHash('sha3-256')
            .update(signature.commitment)
            .update(messageHash)
            .digest();
        // Verify challenge matches
        return signature.challenge.equals(recomputedChallenge);
    }
    // SPHINCS+ (Hash-based Signatures)
    static generateSPHINCSKeyPair() {
        console.log('Generating SPHINCS+ key pair');
        // Generate secret seed
        const secretSeed = crypto.randomBytes(32);
        const publicSeed = crypto.randomBytes(32);
        // Build Merkle tree
        const merkleTree = this.buildMerkleTree(this.SPHINCS_TREE_HEIGHT, secretSeed, publicSeed);
        return {
            publicKey: {
                rootHash: merkleTree.hash,
                publicSeed
            },
            privateKey: {
                secretSeed,
                publicSeed,
                merkleTree
            }
        };
    }
    static sphincsSign(message, privateKey) {
        console.log('Creating SPHINCS+ signature');
        // Hash message to get leaf index
        const messageHash = crypto.createHash('sha3-256').update(message).digest();
        const leafIndex = messageHash.readUInt32BE(0) % (1 << this.SPHINCS_TREE_HEIGHT);
        // Generate one-time signature key
        const otsKey = this.generateOTSKey(privateKey.secretSeed, leafIndex);
        // Create one-time signature
        const otsSignature = this.createOTSSignature(message, otsKey);
        // Get authentication path
        const authPath = this.getAuthenticationPath(privateKey.merkleTree, leafIndex);
        return {
            signature: otsSignature,
            authPath,
            leafIndex
        };
    }
    static sphincsVerify(message, signature, publicKey) {
        console.log('Verifying SPHINCS+ signature');
        // Verify one-time signature
        const otsPublicKey = this.recoverOTSPublicKey(message, signature.signature);
        // Compute leaf hash
        const leafHash = crypto.createHash('sha3-256').update(otsPublicKey).digest();
        // Verify authentication path
        const computedRoot = this.computeRootFromPath(leafHash, signature.authPath, signature.leafIndex);
        return computedRoot.equals(publicKey.rootHash);
    }
    // Helper methods for lattice operations
    static generateSmallVector(dimension) {
        const vector = new Array(dimension);
        for (let i = 0; i < dimension; i++) {
            // Generate small coefficients from {-1, 0, 1}
            vector[i] = Math.floor(Math.random() * 3) - 1;
        }
        return vector;
    }
    static generateRandomMatrix(rows, cols, modulus) {
        const matrix = new Array(rows);
        for (let i = 0; i < rows; i++) {
            matrix[i] = new Array(cols);
            for (let j = 0; j < cols; j++) {
                matrix[i][j] = Math.floor(Math.random() * modulus);
            }
        }
        return matrix;
    }
    static matrixVectorMultiply(matrix, vector, modulus) {
        const result = new Array(matrix.length);
        for (let i = 0; i < matrix.length; i++) {
            result[i] = 0;
            for (let j = 0; j < vector.length; j++) {
                result[i] = (result[i] + matrix[i][j] * vector[j]) % modulus;
            }
        }
        return result;
    }
    static vectorDotProduct(v1, v2, modulus) {
        let result = 0;
        for (let i = 0; i < v1.length; i++) {
            result = (result + v1[i] * v2[i]) % modulus;
        }
        return result;
    }
    static transposeMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const transposed = new Array(cols);
        for (let i = 0; i < cols; i++) {
            transposed[i] = new Array(rows);
            for (let j = 0; j < rows; j++) {
                transposed[i][j] = matrix[j][i];
            }
        }
        return transposed;
    }
    static encodeMessage(message, modulus) {
        // Simple message encoding
        const hash = crypto.createHash('sha3-256').update(message).digest();
        return hash.readUInt32BE(0) % modulus;
    }
    static serializeMatrix(matrix) {
        const flattened = matrix.flat();
        return Buffer.from(flattened.map(x => x & 0xFF));
    }
    // Helper methods for hash-based signatures
    static buildMerkleTree(height, secretSeed, publicSeed) {
        const leafCount = 1 << height;
        const leaves = [];
        // Generate leaf nodes
        for (let i = 0; i < leafCount; i++) {
            const otsKey = this.generateOTSKey(secretSeed, i);
            const leafHash = crypto.createHash('sha3-256').update(otsKey).digest();
            leaves.push({
                hash: leafHash,
                isLeaf: true,
                index: i
            });
        }
        // Build tree bottom-up
        let currentLevel = leaves;
        while (currentLevel.length > 1) {
            const nextLevel = [];
            for (let i = 0; i < currentLevel.length; i += 2) {
                const left = currentLevel[i];
                const right = currentLevel[i + 1];
                const parentHash = crypto.createHash('sha3-256')
                    .update(left.hash)
                    .update(right.hash)
                    .digest();
                nextLevel.push({
                    hash: parentHash,
                    left,
                    right,
                    isLeaf: false
                });
            }
            currentLevel = nextLevel;
        }
        return currentLevel[0];
    }
    static generateOTSKey(secretSeed, index) {
        const hash = crypto.createHash('sha3-512');
        hash.update(secretSeed);
        hash.update(Buffer.from([index & 0xFF, (index >> 8) & 0xFF, (index >> 16) & 0xFF, (index >> 24) & 0xFF]));
        return hash.digest();
    }
    static createOTSSignature(message, otsKey) {
        // Simplified Winternitz one-time signature
        const messageHash = crypto.createHash('sha3-256').update(message).digest();
        const signature = crypto.createHash('sha3-512');
        signature.update(otsKey);
        signature.update(messageHash);
        return signature.digest();
    }
    static recoverOTSPublicKey(message, signature) {
        // Recover public key from signature (simplified)
        const messageHash = crypto.createHash('sha3-256').update(message).digest();
        const publicKey = crypto.createHash('sha3-256');
        publicKey.update(signature);
        publicKey.update(messageHash);
        return publicKey.digest();
    }
    static getAuthenticationPath(tree, leafIndex) {
        const path = [];
        let currentNode = tree;
        let currentIndex = leafIndex;
        // Traverse from root to leaf, collecting sibling hashes
        while (!currentNode.isLeaf) {
            const isLeftChild = (currentIndex % 2) === 0;
            if (isLeftChild) {
                path.push(currentNode.right.hash);
                currentNode = currentNode.left;
            }
            else {
                path.push(currentNode.left.hash);
                currentNode = currentNode.right;
            }
            currentIndex = Math.floor(currentIndex / 2);
        }
        return path;
    }
    static computeRootFromPath(leafHash, authPath, leafIndex) {
        let currentHash = leafHash;
        let currentIndex = leafIndex;
        for (const siblingHash of authPath) {
            const isLeftChild = (currentIndex % 2) === 0;
            const hash = crypto.createHash('sha3-256');
            if (isLeftChild) {
                hash.update(currentHash);
                hash.update(siblingHash);
            }
            else {
                hash.update(siblingHash);
                hash.update(currentHash);
            }
            currentHash = hash.digest();
            currentIndex = Math.floor(currentIndex / 2);
        }
        return currentHash;
    }
}
exports.PostQuantumAlgorithms = PostQuantumAlgorithms;
PostQuantumAlgorithms.KYBER_MODULUS = 3329;
PostQuantumAlgorithms.KYBER_DIMENSIONS = { 1: 256, 3: 768, 5: 1024 };
PostQuantumAlgorithms.DILITHIUM_MODULUS = 8380417;
PostQuantumAlgorithms.SPHINCS_TREE_HEIGHT = 64;
//# sourceMappingURL=PostQuantumAlgorithms.js.map