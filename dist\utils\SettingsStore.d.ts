export interface StoreSchema {
    privacy: {
        blockTrackers: boolean;
        blockAds: boolean;
        blockFingerprinting: boolean;
        spoofTimezone: boolean;
        spoofLanguage: boolean;
        spoofScreen: boolean;
        randomizeCanvasFingerprint: boolean;
        blockWebRTC: boolean;
        clearCookiesOnExit: boolean;
        useDoH: boolean;
    };
    security: {
        enableSandbox: boolean;
        blockDangerousDownloads: boolean;
        enableCSP: boolean;
        blockMixedContent: boolean;
        enableHSTS: boolean;
        blockPlugins: boolean;
        enableMemoryProtection: boolean;
        clearDataOnExit: boolean;
    };
    proxy: {
        currentProxy: {
            type: 'http' | 'https' | 'socks4' | 'socks5' | 'direct';
            host: string;
            port: number;
            username?: string;
            password?: string;
            enabled: boolean;
        } | null;
        proxyList: Array<{
            type: 'http' | 'https' | 'socks4' | 'socks5' | 'direct';
            host: string;
            port: number;
            username?: string;
            password?: string;
            enabled: boolean;
            name?: string;
            description?: string;
        }>;
        rotationEnabled: boolean;
        rotationInterval: number;
    };
    search: {
        defaultProvider: string;
        enableSuggestions: boolean;
        enableHistory: boolean;
        maxSuggestions: number;
        privacyMode: 'strict' | 'balanced' | 'standard';
        customProviders: Array<{
            id: string;
            name: string;
            baseUrl: string;
            searchUrl: string;
            suggestionsUrl?: string;
            icon?: string;
            privacyRating: number;
            features: string[];
            description: string;
        }>;
    };
    steganographic: {
        enableTrafficObfuscation: boolean;
        enableTimingRandomization: boolean;
        enableBehaviorMasking: boolean;
        enableDecoyTraffic: boolean;
        obfuscationIntensity: 'low' | 'medium' | 'high' | 'maximum';
        enableDPIEvasion: boolean;
        enableQuantumResistance: boolean;
        enableCrossPlatformCoordination: boolean;
    };
}
export declare class SettingsStore {
    private store;
    private static instance;
    private constructor();
    static getInstance(): SettingsStore;
    private getDefaultSettings;
    get<K extends keyof StoreSchema>(key: K): StoreSchema[K];
    set<K extends keyof StoreSchema>(key: K, value: StoreSchema[K]): void;
    has<K extends keyof StoreSchema>(key: K): boolean;
    delete<K extends keyof StoreSchema>(key: K): void;
    clear(): void;
    getStorePath(): string;
    reset(): void;
    updatePrivacySettings(settings: Partial<StoreSchema['privacy']>): void;
    updateSecuritySettings(settings: Partial<StoreSchema['security']>): void;
    updateProxySettings(settings: Partial<StoreSchema['proxy']>): void;
    updateSearchSettings(settings: Partial<StoreSchema['search']>): void;
    updateSteganographicSettings(settings: Partial<StoreSchema['steganographic']>): void;
}
export default SettingsStore;
//# sourceMappingURL=SettingsStore.d.ts.map