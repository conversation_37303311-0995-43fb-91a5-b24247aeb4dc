export interface FingerprintProfile {
    userAgent: string;
    screen: {
        width: number;
        height: number;
        colorDepth: number;
        pixelDepth: number;
    };
    timezone: string;
    language: string;
    platform: string;
    hardwareConcurrency: number;
    deviceMemory: number;
    webglVendor: string;
    webglRenderer: string;
    canvasFingerprint: string;
    audioFingerprint: string;
}
export declare class FingerprintProtection {
    private profiles;
    private currentProfile;
    private rotationInterval;
    constructor();
    initialize(): Promise<void>;
    private generateProfiles;
    private createWindowsProfile;
    private createMacProfile;
    private createLinuxProfile;
    private createMobileProfile;
    private generateCanvasFingerprint;
    private generateAudioFingerprint;
    private generateRandomProfile;
    private setupFingerprintProtection;
    private generateProtectionScript;
    private getTimezoneOffset;
    private startProfileRotation;
    private rotateProfile;
    getCurrentProfile(): FingerprintProfile;
    setProfile(profile: FingerprintProfile): void;
    destroy(): void;
}
//# sourceMappingURL=FingerprintProtection.d.ts.map