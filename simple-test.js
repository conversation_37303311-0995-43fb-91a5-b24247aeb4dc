// Simple test for SearchEngine
try {
    const { SearchEngine } = require('./dist/search/SearchEngine');
    const engine = new SearchEngine();
    
    console.log('SearchEngine test:');
    console.log('- Providers:', engine.getProviders().length);
    console.log('- Default:', engine.getDefaultProvider().name);
    console.log('- Search test:', engine.search('test query'));
    console.log('- Process URL:', engine.processInput('example.com'));
    console.log('- Process search:', engine.processInput('test search'));
    
    console.log('✓ All tests passed!');
} catch (error) {
    console.error('Test failed:', error.message);
}
