"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.DiagnosticLogger = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
class DiagnosticLogger {
    constructor() {
        this.logStream = null;
        this.logBuffer = [];
        this.maxBufferSize = 100;
        const userDataPath = electron_1.app.getPath('userData');
        const logsDir = path.join(userDataPath, 'logs');
        // Ensure logs directory exists
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
        // Create timestamped log file
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        this.logFile = path.join(logsDir, `phantom-browser-${timestamp}.log`);
        this.initializeLogFile();
    }
    initializeLogFile() {
        try {
            this.logStream = fs.createWriteStream(this.logFile, { flags: 'a' });
            this.log('INFO', 'STARTUP', 'Diagnostic logging initialized', { logFile: this.logFile });
        }
        catch (error) {
            console.error('Failed to initialize log file:', error);
        }
    }
    log(level, category, message, data) {
        const entry = {
            timestamp: new Date().toISOString(),
            level,
            category,
            message,
            data
        };
        // Add to buffer
        this.logBuffer.push(entry);
        if (this.logBuffer.length > this.maxBufferSize) {
            this.logBuffer.shift();
        }
        // Write to console
        const consoleMessage = `[${entry.timestamp}] ${entry.level} [${entry.category}] ${entry.message}`;
        console.log(consoleMessage, data ? data : '');
        // Write to file
        this.writeToFile(entry);
    }
    writeToFile(entry) {
        if (!this.logStream)
            return;
        try {
            const logLine = `[${entry.timestamp}] ${entry.level} [${entry.category}] ${entry.message}`;
            const dataLine = entry.data ? `\n  DATA: ${JSON.stringify(entry.data, null, 2)}` : '';
            this.logStream.write(`${logLine}${dataLine}\n`);
        }
        catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }
    // Convenience methods for different log levels
    debug(category, message, data) {
        this.log('DEBUG', category, message, data);
    }
    info(category, message, data) {
        this.log('INFO', category, message, data);
    }
    warn(category, message, data) {
        this.log('WARN', category, message, data);
    }
    error(category, message, data) {
        this.log('ERROR', category, message, data);
    }
    // Specific logging methods for different browser events
    logStartup(step, success, details) {
        this.log(success ? 'INFO' : 'ERROR', 'STARTUP', `Startup step: ${step}`, { success, ...details });
    }
    logSearchEngine(action, success, details) {
        this.log(success ? 'INFO' : 'ERROR', 'SEARCH', `SearchEngine: ${action}`, { success, ...details });
    }
    logNavigation(url, method, success, details) {
        this.log(success ? 'INFO' : 'ERROR', 'NAVIGATION', `Navigation to ${url} via ${method}`, { success, url, method, ...details });
    }
    logWebview(event, url, details) {
        this.log('INFO', 'WEBVIEW', `Webview event: ${event}`, { url, ...details });
    }
    logWebviewError(error, url, details) {
        this.log('ERROR', 'WEBVIEW', `Webview error: ${error}`, { url, ...details });
    }
    logNetwork(method, url, status, details) {
        const level = status && status >= 400 ? 'ERROR' : 'INFO';
        this.log(level, 'NETWORK', `${method} ${url}`, { status, ...details });
    }
    getRecentLogs(count = 50) {
        return this.logBuffer.slice(-count);
    }
    getLogFilePath() {
        return this.logFile;
    }
    close() {
        if (this.logStream) {
            this.logStream.end();
            this.logStream = null;
        }
    }
}
exports.DiagnosticLogger = DiagnosticLogger;
// Global logger instance
exports.logger = new DiagnosticLogger();
//# sourceMappingURL=DiagnosticLogger.js.map