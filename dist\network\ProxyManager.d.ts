import { ApiResponse } from '../utils/ErrorHandler';
export interface ProxyConfig {
    type: 'http' | 'https' | 'socks4' | 'socks5' | 'direct';
    host: string;
    port: number;
    username?: string;
    password?: string;
    enabled: boolean;
    name?: string;
    description?: string;
}
export interface VPNConfig {
    provider: string;
    server: string;
    protocol: 'openvpn' | 'wireguard' | 'ikev2';
    credentials: {
        username?: string;
        password?: string;
        certificate?: string;
        privateKey?: string;
    };
    enabled: boolean;
}
export declare class ProxyManager {
    private currentProxy;
    private proxyList;
    private rotationEnabled;
    private rotationInterval;
    private vpnConfig;
    private settingsStore;
    private errorHandler;
    constructor();
    initialize(): Promise<void>;
    private loadSettings;
    private saveSettings;
    private initializeDefaultProxies;
    setProxy(config: ProxyConfig): Promise<void>;
    clearProxy(): Promise<void>;
    private setupProxyAuthentication;
    testProxy(config: ProxyConfig): Promise<boolean>;
    private testSocksProxy;
    private testHttpProxy;
    rotateProxy(): Promise<void>;
    enableProxyRotation(intervalMinutes?: number): void;
    disableProxyRotation(): void;
    validateProxyConfig(config: ProxyConfig): ApiResponse<void>;
    addProxy(config: ProxyConfig): Promise<ApiResponse<void>>;
    removeProxy(host: string, port: number): ApiResponse<void>;
    updateProxy(oldHost: string, oldPort: number, newConfig: ProxyConfig): Promise<ApiResponse<void>>;
    getProxyList(): ProxyConfig[];
    getCurrentProxy(): ProxyConfig | null;
    setupDNSOverHTTPS(): Promise<void>;
    setupTrafficObfuscation(): Promise<void>;
    connectVPN(config: VPNConfig): Promise<void>;
    disconnectVPN(): Promise<void>;
    getVPNStatus(): {
        connected: boolean;
        config?: VPNConfig;
    };
    destroy(): void;
}
//# sourceMappingURL=ProxyManager.d.ts.map