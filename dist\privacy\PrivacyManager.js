"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrivacyManager = void 0;
const electron_1 = require("electron");
const SettingsStore_1 = require("../utils/SettingsStore");
const ErrorHandler_1 = require("../utils/ErrorHandler");
const SecureLogger_1 = require("../utils/SecureLogger");
class PrivacyManager {
    constructor() {
        this.settingsStore = SettingsStore_1.SettingsStore.getInstance();
        this.errorHandler = ErrorHandler_1.ErrorHandler.getInstance();
        this.trackingDomains = new Set();
        this.adBlockRules = [];
        // Load settings from persistent storage
        this.loadSettings();
    }
    async initialize() {
        await this.loadBlockLists();
        await this.configureSession();
        this.setupRequestInterception();
        this.setupScriptInjection();
    }
    async loadBlockLists() {
        // Load tracking domains
        const trackingDomains = [
            'google-analytics.com',
            'googletagmanager.com',
            'facebook.com',
            'doubleclick.net',
            'googlesyndication.com',
            'amazon-adsystem.com',
            'adsystem.amazon.com',
            'scorecardresearch.com',
            'quantserve.com',
            'outbrain.com',
            'taboola.com',
            'addthis.com',
            'sharethis.com',
            'chartbeat.com',
            'hotjar.com',
            'fullstory.com',
            'mouseflow.com',
            'crazyegg.com',
            'mixpanel.com',
            'segment.com',
            'amplitude.com'
        ];
        trackingDomains.forEach(domain => this.trackingDomains.add(domain));
        // Load ad block rules (simplified EasyList format)
        this.adBlockRules = [
            '||googleadservices.com^',
            '||googlesyndication.com^',
            '||doubleclick.net^',
            '||amazon-adsystem.com^',
            '||facebook.com/tr^',
            '||analytics.google.com^',
            '||google-analytics.com^',
            '||googletagmanager.com^'
        ];
    }
    async configureSession() {
        const ses = electron_1.session.defaultSession;
        // Configure DNS over HTTPS
        if (this.settings.useDoH) {
            ses.setProxy({
                mode: 'pac_script',
                pacScript: this.generatePACScript()
            });
        }
        // Block third-party cookies
        ses.cookies.set({
            url: 'https://example.com',
            name: 'SameSite',
            value: 'Strict'
        });
        // Configure user agent
        ses.setUserAgent(this.generateRandomUserAgent());
    }
    setupRequestInterception() {
        const ses = electron_1.session.defaultSession;
        // Block tracking requests
        ses.webRequest.onBeforeRequest({ urls: ['<all_urls>'] }, (details, callback) => {
            const url = new URL(details.url);
            // Check if domain is in tracking list
            if (this.settings.blockTrackers && this.isTrackingDomain(url.hostname)) {
                callback({ cancel: true });
                return;
            }
            // Check ad block rules
            if (this.settings.blockAds && this.matchesAdBlockRule(details.url)) {
                callback({ cancel: true });
                return;
            }
            callback({ cancel: false });
        });
        // Modify headers for privacy
        ses.webRequest.onBeforeSendHeaders({ urls: ['<all_urls>'] }, (details, callback) => {
            const headers = details.requestHeaders;
            // Remove tracking headers
            delete headers['X-Requested-With'];
            delete headers['X-Forwarded-For'];
            delete headers['X-Real-IP'];
            // Spoof referrer
            if (headers['Referer']) {
                headers['Referer'] = this.spoofReferrer(details.url);
            }
            // Add privacy headers
            headers['DNT'] = '1';
            headers['Sec-GPC'] = '1';
            callback({ requestHeaders: headers });
        });
    }
    setupScriptInjection() {
        const ses = electron_1.session.defaultSession;
        // Inject privacy protection scripts
        ses.webRequest.onHeadersReceived({ urls: ['<all_urls>'] }, (details, callback) => {
            if (details.responseHeaders && details.responseHeaders['content-type']?.[0]?.includes('text/html')) {
                // Inject fingerprinting protection script
                const script = this.generateProtectionScript();
                details.responseHeaders['X-Privacy-Script'] = [script];
            }
            callback({ responseHeaders: details.responseHeaders });
        });
    }
    isTrackingDomain(hostname) {
        return Array.from(this.trackingDomains).some(domain => hostname === domain || hostname.endsWith('.' + domain));
    }
    matchesAdBlockRule(url) {
        return this.adBlockRules.some(rule => {
            if (rule.startsWith('||') && rule.endsWith('^')) {
                const domain = rule.slice(2, -1);
                return url.includes(domain);
            }
            return false;
        });
    }
    spoofReferrer(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.hostname}/`;
        }
        catch {
            return 'https://www.google.com/';
        }
    }
    generateRandomUserAgent() {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0'
        ];
        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }
    generatePACScript() {
        return `
            function FindProxyForURL(url, host) {
                // Use DNS over HTTPS
                if (shExpMatch(host, "*.google.com") || 
                    shExpMatch(host, "*.cloudflare.com") ||
                    shExpMatch(host, "*.quad9.net")) {
                    return "HTTPS *******:443; HTTPS *******:443; DIRECT";
                }
                return "DIRECT";
            }
        `;
    }
    generateProtectionScript() {
        return `
            // Canvas fingerprinting protection
            (function() {
                const originalGetContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(type, ...args) {
                    const context = originalGetContext.apply(this, [type, ...args]);
                    if (type === '2d' || type === 'webgl' || type === 'webgl2') {
                        // Add noise to canvas data
                        const originalToDataURL = this.toDataURL;
                        this.toDataURL = function(...args) {
                            const data = originalToDataURL.apply(this, args);
                            return data + Math.random().toString(36).substr(2, 9);
                        };
                    }
                    return context;
                };
            })();

            // WebRTC leak protection
            if (window.RTCPeerConnection) {
                window.RTCPeerConnection = function() {
                    throw new Error('WebRTC is disabled for privacy');
                };
            }

            // Screen resolution spoofing
            Object.defineProperty(screen, 'width', { value: 1920 });
            Object.defineProperty(screen, 'height', { value: 1080 });
            Object.defineProperty(screen, 'availWidth', { value: 1920 });
            Object.defineProperty(screen, 'availHeight', { value: 1040 });

            // Timezone spoofing
            Date.prototype.getTimezoneOffset = function() {
                return 0; // UTC
            };

            // Language spoofing
            Object.defineProperty(navigator, 'language', { value: 'en-US' });
            Object.defineProperty(navigator, 'languages', { value: ['en-US', 'en'] });
        `;
    }
    loadSettings() {
        const result = this.errorHandler.handleSync(() => this.settingsStore.get('privacy'), ErrorHandler_1.ErrorCode.PRIVACY_SETTINGS_LOAD_FAILED, 'Failed to load privacy settings from storage');
        if (result.success) {
            this.settings = result.data;
            SecureLogger_1.secureLogger.info('Privacy settings loaded from storage', 'PrivacyManager');
        }
        else {
            this.errorHandler.logError(result.error, 'PrivacyManager.loadSettings');
            // Use default settings if loading fails
            this.settings = {
                blockTrackers: true,
                blockAds: true,
                blockFingerprinting: true,
                spoofTimezone: true,
                spoofLanguage: true,
                spoofScreen: true,
                randomizeCanvasFingerprint: true,
                blockWebRTC: true,
                clearCookiesOnExit: true,
                useDoH: false // Disabled by default due to Electron compatibility issues
            };
        }
    }
    saveSettings() {
        return this.errorHandler.handleSync(() => this.settingsStore.updatePrivacySettings(this.settings), ErrorHandler_1.ErrorCode.PRIVACY_SETTINGS_SAVE_FAILED, 'Failed to save privacy settings to storage');
    }
    updateSettings(newSettings) {
        try {
            this.errorHandler.validateInput(typeof newSettings === 'object' && newSettings !== null, 'Invalid settings object provided');
            this.settings = { ...this.settings, ...newSettings };
            const saveResult = this.saveSettings();
            if (!saveResult.success) {
                // Revert settings on save failure
                this.loadSettings();
                return saveResult;
            }
            return this.errorHandler.createSuccessResponse(undefined, 'Privacy settings updated successfully');
        }
        catch (error) {
            this.errorHandler.logError(error, 'PrivacyManager.updateSettings', { newSettings });
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.PRIVACY_SETTINGS_SAVE_FAILED);
        }
    }
    getSettings() {
        return { ...this.settings };
    }
    async clearPrivacyData() {
        return this.errorHandler.handleAsync(async () => {
            const ses = electron_1.session.defaultSession;
            // Clear different types of data with individual error handling
            const clearOperations = [
                { name: 'storage data', operation: () => ses.clearStorageData() },
                { name: 'cache', operation: () => ses.clearCache() },
                { name: 'auth cache', operation: () => ses.clearAuthCache() },
                { name: 'cookies', operation: () => ses.cookies.flushStore() }
            ];
            const errors = [];
            for (const { name, operation } of clearOperations) {
                try {
                    await operation();
                    SecureLogger_1.secureLogger.info(`Successfully cleared ${name}`, 'PrivacyManager');
                }
                catch (error) {
                    const errorMsg = `Failed to clear ${name}: ${error instanceof Error ? error.message : String(error)}`;
                    errors.push(errorMsg);
                    SecureLogger_1.secureLogger.error(errorMsg, 'PrivacyManager');
                }
            }
            if (errors.length > 0) {
                throw new Error(`Some data could not be cleared: ${errors.join(', ')}`);
            }
            SecureLogger_1.secureLogger.info('All privacy data cleared successfully', 'PrivacyManager');
        }, ErrorHandler_1.ErrorCode.CLEAR_BROWSING_DATA_FAILED, 'Failed to clear privacy data');
    }
}
exports.PrivacyManager = PrivacyManager;
//# sourceMappingURL=PrivacyManager.js.map