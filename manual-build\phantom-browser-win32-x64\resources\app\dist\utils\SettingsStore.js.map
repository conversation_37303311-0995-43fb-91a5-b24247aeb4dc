{"version": 3, "file": "SettingsStore.js", "sourceRoot": "", "sources": ["../../src/utils/SettingsStore.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAAmC;AA6EnC,MAAa,aAAa;IAItB;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,wBAAK,CAAc;YAChC,IAAI,EAAE,0BAA0B;YAChC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACnC,MAAM,EAAE;gBACJ,OAAO,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,aAAa,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAClC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC7B,mBAAmB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACxC,aAAa,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAClC,aAAa,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAClC,WAAW,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAChC,0BAA0B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC/C,WAAW,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAChC,kBAAkB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACvC,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;qBAC9B;iBACJ;gBACD,QAAQ,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,aAAa,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAClC,uBAAuB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC5C,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC9B,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACtC,UAAU,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC/B,YAAY,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACjC,sBAAsB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC3C,eAAe,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;qBACvC;iBACJ;gBACD,KAAK,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,YAAY,EAAE,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;wBAC1C,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;wBAC5B,eAAe,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACpC,gBAAgB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACvC;iBACJ;gBACD,MAAM,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACnC,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACtC,aAAa,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAClC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAClC,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE;wBACzE,eAAe,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;qBACrC;iBACJ;gBACD,cAAc,EAAE;oBACZ,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,wBAAwB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC7C,yBAAyB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC9C,qBAAqB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC1C,kBAAkB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACvC,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;wBACpF,gBAAgB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBACrC,uBAAuB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC5C,+BAA+B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;qBACvD;iBACJ;aACJ;SACJ,CAAC,CAAC;IACP,CAAC;IAEM,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC1B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAClC,CAAC;IAEO,kBAAkB;QACtB,OAAO;YACH,OAAO,EAAE;gBACL,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,IAAI;gBACd,mBAAmB,EAAE,IAAI;gBACzB,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,IAAI;gBACjB,0BAA0B,EAAE,IAAI;gBAChC,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,MAAM,EAAE,IAAI;aACf;YACD,QAAQ,EAAE;gBACN,aAAa,EAAE,IAAI;gBACnB,uBAAuB,EAAE,IAAI;gBAC7B,SAAS,EAAE,IAAI;gBACf,iBAAiB,EAAE,IAAI;gBACvB,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,IAAI;gBAClB,sBAAsB,EAAE,IAAI;gBAC5B,eAAe,EAAE,IAAI;aACxB;YACD,KAAK,EAAE;gBACH,YAAY,EAAE,IAAI;gBAClB,SAAS,EAAE;oBACP;wBACI,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,EAAE;wBACR,IAAI,EAAE,CAAC;wBACP,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,mBAAmB;wBACzB,WAAW,EAAE,uCAAuC;qBACvD;iBACJ;gBACD,eAAe,EAAE,KAAK;gBACtB,gBAAgB,EAAE,EAAE;aACvB;YACD,MAAM,EAAE;gBACJ,eAAe,EAAE,YAAY;gBAC7B,iBAAiB,EAAE,IAAI;gBACvB,aAAa,EAAE,IAAI;gBACnB,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,UAAU;gBACvB,eAAe,EAAE,EAAE;aACtB;YACD,cAAc,EAAE;gBACZ,wBAAwB,EAAE,IAAI;gBAC9B,yBAAyB,EAAE,IAAI;gBAC/B,qBAAqB,EAAE,IAAI;gBAC3B,kBAAkB,EAAE,IAAI;gBACxB,oBAAoB,EAAE,MAAM;gBAC5B,gBAAgB,EAAE,IAAI;gBACtB,uBAAuB,EAAE,IAAI;gBAC7B,+BAA+B,EAAE,IAAI;aACxC;SACJ,CAAC;IACN,CAAC;IAED,yCAAyC;IAClC,GAAG,CAA8B,GAAM;QAC1C,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEM,GAAG,CAA8B,GAAM,EAAE,KAAqB;QACjE,IAAI,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,cAAc,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjH,CAAC;IACL,CAAC;IAEM,GAAG,CAA8B,GAAM;QAC1C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAEM,MAAM,CAA8B,GAAM;QAC7C,IAAI,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,EAAE,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,oBAAoB,GAAG,cAAc,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnH,CAAC;IACL,CAAC;IAEM,KAAK;QACR,IAAI,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3G,CAAC;IACL,CAAC;IAEM,YAAY;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAC3B,CAAC;IAEM,KAAK;QACR,IAAI,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3G,CAAC;IACL,CAAC;IAED,gDAAgD;IACzC,qBAAqB,CAAC,QAAyC;QAClE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;IAC7D,CAAC;IAEM,sBAAsB,CAAC,QAA0C;QACpE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEM,mBAAmB,CAAC,QAAuC;QAC9D,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEM,oBAAoB,CAAC,QAAwC;QAChE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;IAC5D,CAAC;IAEM,4BAA4B,CAAC,QAAgD;QAChF,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACnD,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;IACpE,CAAC;CACJ;AAhOD,sCAgOC;AAED,kBAAe,aAAa,CAAC"}