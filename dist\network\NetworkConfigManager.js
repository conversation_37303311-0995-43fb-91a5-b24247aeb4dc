"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkConfigManager = void 0;
const electron_1 = require("electron");
const ErrorHandler_1 = require("../utils/ErrorHandler");
const SettingsStore_1 = require("../utils/SettingsStore");
const SecureLogger_1 = require("../utils/SecureLogger");
class NetworkConfigManager {
    constructor(proxyManager) {
        this.proxyManager = proxyManager;
        this.errorHandler = ErrorHandler_1.ErrorHandler.getInstance();
        this.settingsStore = SettingsStore_1.SettingsStore.getInstance();
        this.dohProviders = [
            {
                name: 'Cloudflare',
                url: 'https://*******/dns-query',
                description: 'Fast and privacy-focused DNS service',
                privacyRating: 9
            },
            {
                name: 'Quad9',
                url: 'https://*******/dns-query',
                description: 'Security-focused DNS with malware blocking',
                privacyRating: 8
            },
            {
                name: 'Google',
                url: 'https://*******/dns-query',
                description: 'Google Public DNS service',
                privacyRating: 6
            },
            {
                name: 'OpenDNS',
                url: 'https://**************/dns-query',
                description: 'Cisco OpenDNS service',
                privacyRating: 7
            }
        ];
        this.currentConfig = {
            priority: 'direct',
            status: 'inactive',
            lastUpdated: Date.now()
        };
        this.loadConfiguration();
    }
    loadConfiguration() {
        try {
            // Load network configuration from settings
            const networkSettings = this.settingsStore.get('proxy');
            if (networkSettings.currentProxy) {
                this.currentConfig = {
                    priority: 'proxy',
                    proxyConfig: networkSettings.currentProxy,
                    status: 'active',
                    lastUpdated: Date.now()
                };
            }
            else {
                // Check if DoH is enabled in privacy settings
                const privacySettings = this.settingsStore.get('privacy');
                if (privacySettings.useDoH) {
                    this.currentConfig = {
                        priority: 'doh',
                        dohConfig: {
                            enabled: true,
                            providers: ['https://*******/dns-query'],
                            fallbackToDirect: true
                        },
                        status: 'active',
                        lastUpdated: Date.now()
                    };
                }
            }
            SecureLogger_1.secureLogger.info('Network configuration loaded successfully', 'NetworkConfigManager');
        }
        catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.loadConfiguration');
            // Use default direct configuration
            this.currentConfig = {
                priority: 'direct',
                status: 'active',
                lastUpdated: Date.now()
            };
        }
    }
    saveConfiguration() {
        try {
            // Save the current configuration state
            // This is handled by the individual managers (ProxyManager, PrivacyManager)
            SecureLogger_1.secureLogger.info('Network configuration state updated', 'NetworkConfigManager');
            return this.errorHandler.createSuccessResponse(undefined, 'Network configuration saved');
        }
        catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.saveConfiguration');
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.STORAGE_WRITE_FAILED);
        }
    }
    async setNetworkConfiguration(config) {
        try {
            const newConfig = {
                ...this.currentConfig,
                ...config,
                lastUpdated: Date.now()
            };
            // Validate the configuration
            const validationResult = this.validateConfiguration(newConfig);
            if (!validationResult.success) {
                return validationResult;
            }
            // Apply the configuration based on priority
            const applyResult = await this.applyConfiguration(newConfig);
            if (!applyResult.success) {
                return applyResult;
            }
            this.currentConfig = newConfig;
            this.saveConfiguration();
            return this.errorHandler.createSuccessResponse(undefined, `Network configuration updated: ${newConfig.priority} mode active`);
        }
        catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.setNetworkConfiguration', { config });
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.NETWORK_CONNECTION_FAILED);
        }
    }
    validateConfiguration(config) {
        try {
            // Validate priority
            this.errorHandler.validateInput(['proxy', 'doh', 'direct'].includes(config.priority), 'Invalid network priority. Must be proxy, doh, or direct');
            // Validate proxy configuration if priority is proxy
            if (config.priority === 'proxy') {
                this.errorHandler.validateInput(!!config.proxyConfig, 'Proxy configuration is required when priority is proxy');
                if (config.proxyConfig) {
                    const proxyValidation = this.proxyManager.validateProxyConfig(config.proxyConfig);
                    if (!proxyValidation.success) {
                        return proxyValidation;
                    }
                }
            }
            // Validate DoH configuration if priority is doh
            if (config.priority === 'doh') {
                this.errorHandler.validateInput(!!config.dohConfig && config.dohConfig.enabled, 'DoH configuration is required when priority is doh');
                if (config.dohConfig) {
                    this.errorHandler.validateInput(config.dohConfig.providers.length > 0, 'At least one DoH provider is required');
                }
            }
            return this.errorHandler.createSuccessResponse(undefined, 'Configuration is valid');
        }
        catch (error) {
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.INVALID_INPUT);
        }
    }
    async applyConfiguration(config) {
        try {
            const ses = electron_1.session.defaultSession;
            // Clear any existing configuration first
            await this.clearAllNetworkConfiguration();
            switch (config.priority) {
                case 'proxy':
                    if (config.proxyConfig) {
                        await this.proxyManager.setProxy(config.proxyConfig);
                        SecureLogger_1.secureLogger.info('Proxy configuration applied', 'NetworkConfigManager');
                    }
                    break;
                case 'doh':
                    if (config.dohConfig) {
                        await this.setupDoH(config.dohConfig);
                        SecureLogger_1.secureLogger.info('DoH configuration applied', 'NetworkConfigManager');
                    }
                    break;
                case 'direct':
                    // Direct connection - no additional configuration needed
                    SecureLogger_1.secureLogger.info('Direct connection configured', 'NetworkConfigManager');
                    break;
                default:
                    throw new Error(`Unknown network priority: ${config.priority}`);
            }
            return this.errorHandler.createSuccessResponse(undefined, 'Configuration applied successfully');
        }
        catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.applyConfiguration', { config });
            return this.errorHandler.createErrorResponse(error, ErrorHandler_1.ErrorCode.NETWORK_CONNECTION_FAILED);
        }
    }
    async clearAllNetworkConfiguration() {
        try {
            const ses = electron_1.session.defaultSession;
            // Clear proxy settings
            await ses.setProxy({ proxyRules: 'direct://' });
            // Clear DoH settings (reset to system default)
            await ses.setProxy({ mode: 'system' });
            SecureLogger_1.secureLogger.info('All network configurations cleared', 'NetworkConfigManager');
        }
        catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.clearAllNetworkConfiguration');
            throw error;
        }
    }
    async setupDoH(dohConfig) {
        try {
            const ses = electron_1.session.defaultSession;
            // IMPORTANT: Electron doesn't support DoH through PAC scripts
            // DoH should be configured at the system level or through Electron's secure DNS API
            // For now, we'll use direct connection and log the DoH preference
            SecureLogger_1.secureLogger.warn('DoH configuration requested but not supported in current Electron version', 'NetworkConfigManager');
            SecureLogger_1.secureLogger.info('Falling back to direct connection for compatibility', 'NetworkConfigManager');
            // Clear any existing proxy configuration and use direct connection
            await ses.setProxy({ proxyRules: 'direct://' });
            SecureLogger_1.secureLogger.info(`DoH fallback: using direct connection instead of ${dohConfig.providers.length} providers`, 'NetworkConfigManager');
        }
        catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.setupDoH', { dohConfig });
            throw error;
        }
    }
    generateDoHPacScript(providers, fallbackToDirect) {
        // This method is deprecated due to PAC script incompatibility with DoH
        // Keeping for backward compatibility but not used
        SecureLogger_1.secureLogger.warn('generateDoHPacScript called but DoH PAC scripts are not supported', 'NetworkConfigManager');
        return `
            function FindProxyForURL(url, host) {
                // DoH not supported via PAC script - use direct connection
                return "DIRECT";
            }
        `;
    }
    getCurrentConfiguration() {
        return { ...this.currentConfig };
    }
    getDoHProviders() {
        return [...this.dohProviders];
    }
    async switchToProxy(proxyConfig) {
        return this.setNetworkConfiguration({
            priority: 'proxy',
            proxyConfig,
            status: 'active'
        });
    }
    async switchToDoH(providers) {
        const dohProviders = providers || ['https://*******/dns-query'];
        return this.setNetworkConfiguration({
            priority: 'doh',
            dohConfig: {
                enabled: true,
                providers: dohProviders,
                fallbackToDirect: true
            },
            status: 'active'
        });
    }
    async switchToDirect() {
        return this.setNetworkConfiguration({
            priority: 'direct',
            status: 'active'
        });
    }
    getNetworkStatus() {
        const config = this.currentConfig;
        let details = '';
        switch (config.priority) {
            case 'proxy':
                details = config.proxyConfig
                    ? `${config.proxyConfig.type.toUpperCase()} ${config.proxyConfig.host}:${config.proxyConfig.port}`
                    : 'Proxy configuration missing';
                break;
            case 'doh':
                details = config.dohConfig
                    ? `DoH providers: ${config.dohConfig.providers.length}`
                    : 'DoH configuration missing';
                break;
            case 'direct':
                details = 'Direct internet connection';
                break;
        }
        return {
            mode: config.priority,
            active: config.status === 'active',
            details,
            lastUpdated: new Date(config.lastUpdated)
        };
    }
    destroy() {
        // Cleanup method
        this.clearAllNetworkConfiguration().catch(error => {
            this.errorHandler.logError(error, 'NetworkConfigManager.destroy');
        });
    }
}
exports.NetworkConfigManager = NetworkConfigManager;
exports.default = NetworkConfigManager;
//# sourceMappingURL=NetworkConfigManager.js.map