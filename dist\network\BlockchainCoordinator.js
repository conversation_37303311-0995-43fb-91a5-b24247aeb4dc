"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockchainCoordinator = void 0;
const crypto = __importStar(require("crypto"));
class BlockchainCoordinator {
    constructor(nodeId, privateKey) {
        this.chain = [];
        this.pendingTransactions = [];
        this.consensusVotes = new Map();
        this.validators = new Set();
        this.difficulty = 2; // Proof of work difficulty
        this.nodeId = nodeId;
        this.privateKey = privateKey;
        this.createGenesisBlock();
    }
    createGenesisBlock() {
        const genesisData = {
            type: 'pattern_proposal',
            nodeId: 'genesis',
            payload: { message: 'Phantom Network Genesis Block' },
            signature: ''
        };
        const genesisBlock = {
            index: 0,
            timestamp: Date.now(),
            data: genesisData,
            previousHash: '0',
            hash: '',
            nonce: 0,
            validator: 'genesis'
        };
        genesisBlock.hash = this.calculateHash(genesisBlock);
        this.chain.push(genesisBlock);
        console.log('Genesis block created');
    }
    addTransaction(data) {
        // Verify signature before adding
        if (this.verifySignature(data)) {
            this.pendingTransactions.push(data);
            console.log(`Transaction added: ${data.type} from ${data.nodeId}`);
        }
        else {
            console.warn(`Invalid signature for transaction from ${data.nodeId}`);
        }
    }
    verifySignature(data) {
        // Simplified signature verification
        // In a real implementation, this would use proper cryptographic verification
        return data.signature.length > 0;
    }
    async mineBlock() {
        if (this.pendingTransactions.length === 0) {
            return null;
        }
        const previousBlock = this.getLatestBlock();
        const newBlock = {
            index: previousBlock.index + 1,
            timestamp: Date.now(),
            data: this.pendingTransactions[0], // Take first transaction
            previousHash: previousBlock.hash,
            hash: '',
            nonce: 0,
            validator: this.nodeId
        };
        // Proof of work mining
        newBlock.hash = await this.mineBlockWithProofOfWork(newBlock);
        // Clear the mined transaction
        this.pendingTransactions.shift();
        return newBlock;
    }
    async mineBlockWithProofOfWork(block) {
        const target = '0'.repeat(this.difficulty);
        while (true) {
            const hash = this.calculateHash(block);
            if (hash.substring(0, this.difficulty) === target) {
                console.log(`Block mined: ${hash} (nonce: ${block.nonce})`);
                return hash;
            }
            block.nonce++;
            // Yield control periodically to prevent blocking
            if (block.nonce % 1000 === 0) {
                await new Promise(resolve => setTimeout(resolve, 1));
            }
        }
    }
    calculateHash(block) {
        const blockData = `${block.index}${block.timestamp}${JSON.stringify(block.data)}${block.previousHash}${block.nonce}${block.validator}`;
        return crypto.createHash('sha256').update(blockData).digest('hex');
    }
    async proposeBlock(block) {
        // Validate the proposed block
        if (!this.isValidBlock(block)) {
            console.warn('Invalid block proposed');
            return false;
        }
        // Start consensus process
        const consensus = {
            blockHash: block.hash,
            votes: [],
            status: 'pending',
            threshold: Math.ceil(this.validators.size * 0.67), // 67% consensus
        };
        this.consensusVotes.set(block.hash, consensus);
        // Auto-vote if this node is a validator
        if (this.validators.has(this.nodeId)) {
            await this.voteOnBlock(block.hash, 'approve');
        }
        console.log(`Block proposed for consensus: ${block.hash}`);
        return true;
    }
    async voteOnBlock(blockHash, vote) {
        const consensus = this.consensusVotes.get(blockHash);
        if (!consensus || consensus.status !== 'pending') {
            console.warn('Cannot vote on this block');
            return;
        }
        // Check if node already voted
        const existingVote = consensus.votes.find(v => v.nodeId === this.nodeId);
        if (existingVote) {
            console.warn('Node already voted on this block');
            return;
        }
        const voteData = {
            blockHash,
            nodeId: this.nodeId,
            vote,
            timestamp: Date.now(),
            signature: this.signVote(blockHash, vote)
        };
        consensus.votes.push(voteData);
        // Check if consensus is reached
        await this.checkConsensus(blockHash);
    }
    signVote(blockHash, vote) {
        const voteData = `${blockHash}${this.nodeId}${vote}`;
        return crypto.createHash('sha256').update(voteData + this.privateKey.toString('hex')).digest('hex');
    }
    async checkConsensus(blockHash) {
        const consensus = this.consensusVotes.get(blockHash);
        if (!consensus || consensus.status !== 'pending')
            return;
        const approveVotes = consensus.votes.filter(v => v.vote === 'approve').length;
        const rejectVotes = consensus.votes.filter(v => v.vote === 'reject').length;
        if (approveVotes >= consensus.threshold) {
            consensus.status = 'approved';
            consensus.finalizedAt = Date.now();
            await this.finalizeBlock(blockHash);
            console.log(`Block ${blockHash} approved by consensus`);
        }
        else if (rejectVotes > this.validators.size - consensus.threshold) {
            consensus.status = 'rejected';
            consensus.finalizedAt = Date.now();
            console.log(`Block ${blockHash} rejected by consensus`);
        }
    }
    async finalizeBlock(blockHash) {
        // Find the block and add it to the chain
        // In a real implementation, this would retrieve the full block data
        console.log(`Finalizing block: ${blockHash}`);
        // Execute the coordination data in the block
        const consensus = this.consensusVotes.get(blockHash);
        if (consensus) {
            // Process the coordination command
            console.log('Executing coordination command from finalized block');
        }
    }
    isValidBlock(block) {
        // Validate block structure and hash
        const calculatedHash = this.calculateHash(block);
        if (calculatedHash !== block.hash) {
            return false;
        }
        // Validate proof of work
        const target = '0'.repeat(this.difficulty);
        if (block.hash.substring(0, this.difficulty) !== target) {
            return false;
        }
        // Validate previous hash
        const previousBlock = this.getLatestBlock();
        if (block.previousHash !== previousBlock.hash) {
            return false;
        }
        // Validate index
        if (block.index !== previousBlock.index + 1) {
            return false;
        }
        return true;
    }
    getLatestBlock() {
        return this.chain[this.chain.length - 1];
    }
    getChain() {
        return [...this.chain];
    }
    isChainValid() {
        for (let i = 1; i < this.chain.length; i++) {
            const currentBlock = this.chain[i];
            const previousBlock = this.chain[i - 1];
            if (currentBlock.hash !== this.calculateHash(currentBlock)) {
                return false;
            }
            if (currentBlock.previousHash !== previousBlock.hash) {
                return false;
            }
        }
        return true;
    }
    addValidator(nodeId) {
        this.validators.add(nodeId);
        console.log(`Validator added: ${nodeId}`);
    }
    removeValidator(nodeId) {
        this.validators.delete(nodeId);
        console.log(`Validator removed: ${nodeId}`);
    }
    getValidators() {
        return Array.from(this.validators);
    }
    getPendingTransactions() {
        return [...this.pendingTransactions];
    }
    getConsensusStatus(blockHash) {
        return this.consensusVotes.get(blockHash) || null;
    }
    // Coordination-specific methods
    async proposePatternExecution(patternId, participants, startTime) {
        const coordinationData = {
            type: 'pattern_execution',
            nodeId: this.nodeId,
            payload: {
                patternId,
                participants,
                startTime,
                proposedBy: this.nodeId
            },
            signature: this.signCoordinationData('pattern_execution', { patternId, participants, startTime })
        };
        this.addTransaction(coordinationData);
        // Mine a block with this transaction
        const block = await this.mineBlock();
        if (block) {
            await this.proposeBlock(block);
            return block.hash;
        }
        throw new Error('Failed to mine block for pattern execution');
    }
    signCoordinationData(type, payload) {
        const data = `${type}${this.nodeId}${JSON.stringify(payload)}`;
        return crypto.createHash('sha256').update(data + this.privateKey.toString('hex')).digest('hex');
    }
    async announceNodeJoin(nodeCapabilities) {
        const coordinationData = {
            type: 'node_join',
            nodeId: this.nodeId,
            payload: {
                capabilities: nodeCapabilities,
                timestamp: Date.now()
            },
            signature: this.signCoordinationData('node_join', nodeCapabilities)
        };
        this.addTransaction(coordinationData);
    }
    async announceNodeLeave(reason) {
        const coordinationData = {
            type: 'node_leave',
            nodeId: this.nodeId,
            payload: {
                reason,
                timestamp: Date.now()
            },
            signature: this.signCoordinationData('node_leave', { reason })
        };
        this.addTransaction(coordinationData);
    }
    getNetworkState() {
        return {
            chainLength: this.chain.length,
            pendingTransactions: this.pendingTransactions.length,
            validators: this.validators.size,
            pendingConsensus: Array.from(this.consensusVotes.values()).filter(c => c.status === 'pending').length,
            isValid: this.isChainValid()
        };
    }
    // Cleanup old consensus votes
    cleanupOldConsensus() {
        const oneHourAgo = Date.now() - 3600000;
        for (const [blockHash, consensus] of this.consensusVotes) {
            if (consensus.finalizedAt && consensus.finalizedAt < oneHourAgo) {
                this.consensusVotes.delete(blockHash);
            }
        }
    }
}
exports.BlockchainCoordinator = BlockchainCoordinator;
//# sourceMappingURL=BlockchainCoordinator.js.map