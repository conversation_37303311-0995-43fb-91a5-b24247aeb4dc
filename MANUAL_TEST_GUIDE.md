# Phantom Browser SearchEngine Manual Test Guide

## 🧪 Quick Test Instructions

### Test 1: Startup Homepage Loading
1. **Launch the browser:** Double-click any of these executables:
   - `executable-working/phantom-browser-working-win32-x64/phantom-browser-working.exe`
   - `manual-build/phantom-browser-win32-x64/Phantom Browser.exe`

2. **Expected Result:**
   - ✅ <PERSON><PERSON><PERSON> should load DuckDuckGo homepage (https://duckduckgo.com)
   - ✅ Address bar should show "https://duckduckgo.com"
   - ✅ No blank page or "about:blank"

### Test 2: Address Bar Search
1. **Clear address bar** and type: `privacy browser`
2. **Press Enter**
3. **Expected Result:**
   - ✅ Should navigate to DuckDuckGo search results
   - ✅ URL should be: `https://duckduckgo.com/?q=privacy%20browser&t=phantom`

### Test 3: URL Navigation
1. **Clear address bar** and type: `example.com`
2. **Press Enter**
3. **Expected Result:**
   - ✅ Should navigate directly to https://example.com
   - ✅ Should NOT search for "example.com"

### Test 4: Search Provider Selection
1. **Click the settings/privacy button** (⚙ icon)
2. **Find "Search Engine" section**
3. **Change default provider** to "Startpage"
4. **Type a search query** in address bar
5. **Expected Result:**
   - ✅ Should search with Startpage instead of DuckDuckGo
   - ✅ URL should contain "startpage.com"

### Test 5: Multiple Search Providers
**Available providers to test:**
- **DuckDuckGo** - https://duckduckgo.com
- **Startpage** - https://www.startpage.com
- **SearX** - https://searx.org
- **Brave Search** - https://search.brave.com
- **Yandex** - https://yandex.com
- **Bing** - https://www.bing.com

## 🔍 Troubleshooting

### If browser shows blank page on startup:
1. Check browser console (F12) for errors
2. Look for "Loading default search engine homepage" message
3. Verify SearchEngine initialization messages

### If search doesn't work:
1. Check that address bar has focus
2. Verify Enter key is being pressed
3. Check console for SearchEngine errors

### If wrong search provider is used:
1. Open privacy panel
2. Check selected search provider
3. Verify provider change is saved

## ✅ Success Criteria

**All tests pass if:**
- [x] Browser loads DuckDuckGo homepage on startup
- [x] Search queries work from address bar
- [x] URL navigation works correctly
- [x] Search provider can be changed
- [x] All 6 search providers are available
- [x] No console errors during normal operation

## 🎯 Expected Performance

- **Startup Time:** Under 10 seconds
- **Homepage Load:** Under 3 seconds
- **Search Response:** Immediate (< 1 second)
- **Provider Switch:** Immediate

## 📝 Test Results Template

```
Test Date: ___________
Tester: ___________

[ ] Test 1: Startup Homepage Loading
[ ] Test 2: Address Bar Search  
[ ] Test 3: URL Navigation
[ ] Test 4: Search Provider Selection
[ ] Test 5: Multiple Search Providers

Issues Found:
_________________________________
_________________________________

Overall Status: PASS / FAIL
```
