// Comprehensive validation script for SearchEngine build
const fs = require('fs');
const path = require('path');

console.log('🔍 Phantom Browser SearchEngine Build Validation');
console.log('================================================\n');

// Test 1: Verify compiled SearchEngine functionality
function testCompiledSearchEngine() {
    console.log('📦 Test 1: Compiled SearchEngine Functionality');
    console.log('----------------------------------------------');
    
    try {
        const { SearchEngine } = require('./dist/search/SearchEngine');
        const engine = new SearchEngine();
        
        // Test basic functionality
        const providers = engine.getProviders();
        const defaultProvider = engine.getDefaultProvider();
        
        console.log(`✅ SearchEngine loaded successfully`);
        console.log(`✅ Found ${providers.length} search providers`);
        console.log(`✅ Default provider: ${defaultProvider.name}`);
        
        // Test search functionality
        const searchUrl = engine.search('test query');
        console.log(`✅ Search URL generation: ${searchUrl}`);
        
        // Test processInput functionality
        const urlResult = engine.processInput('example.com');
        const searchResult = engine.processInput('test search');
        
        console.log(`✅ URL processing: ${urlResult.type} -> ${urlResult.value}`);
        console.log(`✅ Search processing: ${searchResult.type} -> ${searchResult.value}`);
        
        // Test new getDefaultHomepage method
        if (engine.getDefaultHomepage) {
            const homepage = engine.getDefaultHomepage();
            console.log(`✅ Default homepage: ${homepage}`);
        } else {
            console.log('❌ getDefaultHomepage method missing');
            return false;
        }
        
        return true;
    } catch (error) {
        console.log(`❌ SearchEngine test failed: ${error.message}`);
        return false;
    }
}

// Test 2: Verify distribution files are updated
function testDistributionFiles() {
    console.log('\n📁 Test 2: Distribution Files Verification');
    console.log('------------------------------------------');
    
    const distributions = [
        'executable-working/phantom-browser-working-win32-x64/resources/app',
        'manual-build/phantom-browser-win32-x64/resources/app',
        'executable/phantom-browser-win32-x64/resources/app',
        'executable-fixed/phantom-browser-fixed-win32-x64/resources/app',
        'manual-build/phantom-browser-production'
    ];
    
    let allValid = true;
    
    distributions.forEach(distPath => {
        const searchEnginePath = path.join(distPath, 'dist/search/SearchEngine.js');
        const rendererPath = path.join(distPath, 'renderer/renderer.js');
        
        console.log(`\nChecking: ${distPath}`);
        
        // Check SearchEngine.js
        if (fs.existsSync(searchEnginePath)) {
            const content = fs.readFileSync(searchEnginePath, 'utf8');
            if (content.includes('getDefaultHomepage')) {
                console.log('  ✅ SearchEngine.js updated with getDefaultHomepage');
            } else {
                console.log('  ❌ SearchEngine.js missing getDefaultHomepage method');
                allValid = false;
            }
        } else {
            console.log('  ❌ SearchEngine.js not found');
            allValid = false;
        }
        
        // Check renderer.js
        if (fs.existsSync(rendererPath)) {
            const content = fs.readFileSync(rendererPath, 'utf8');
            if (content.includes('loadDefaultHomepage')) {
                console.log('  ✅ renderer.js updated with loadDefaultHomepage');
            } else {
                console.log('  ❌ renderer.js missing loadDefaultHomepage method');
                allValid = false;
            }
        } else {
            console.log('  ❌ renderer.js not found');
            allValid = false;
        }
    });
    
    return allValid;
}

// Test 3: Verify search providers
function testSearchProviders() {
    console.log('\n🔍 Test 3: Search Providers Verification');
    console.log('----------------------------------------');
    
    try {
        const { SearchEngine } = require('./dist/search/SearchEngine');
        const engine = new SearchEngine();
        const providers = engine.getProviders();
        
        const expectedProviders = ['duckduckgo', 'startpage', 'searx', 'brave', 'yandex', 'bing'];
        const foundProviders = providers.map(p => p.id);
        
        console.log('Expected providers:', expectedProviders);
        console.log('Found providers:', foundProviders);
        
        const allFound = expectedProviders.every(id => foundProviders.includes(id));
        
        if (allFound) {
            console.log('✅ All expected search providers found');
            
            // Test each provider
            providers.forEach(provider => {
                console.log(`  - ${provider.name} (Privacy: ${provider.privacyRating}/10)`);
                console.log(`    URL: ${provider.baseUrl}`);
                console.log(`    Search: ${provider.searchUrl}`);
            });
            
            return true;
        } else {
            console.log('❌ Some search providers missing');
            return false;
        }
    } catch (error) {
        console.log(`❌ Provider test failed: ${error.message}`);
        return false;
    }
}

// Run all tests
async function runValidation() {
    const test1 = testCompiledSearchEngine();
    const test2 = testDistributionFiles();
    const test3 = testSearchProviders();
    
    console.log('\n🎯 Validation Summary');
    console.log('====================');
    
    if (test1 && test2 && test3) {
        console.log('🎉 ALL TESTS PASSED! SearchEngine build is successful.');
        console.log('\n✅ Key Features Verified:');
        console.log('  - SearchEngine compiles and loads correctly');
        console.log('  - All distribution files updated');
        console.log('  - 6 search providers available');
        console.log('  - Default homepage loading implemented');
        console.log('  - Address bar search functionality working');
        console.log('  - URL vs search query detection working');
        
        console.log('\n🚀 Ready for Testing:');
        console.log('  1. Browser loads DuckDuckGo homepage on startup');
        console.log('  2. Address bar search queries work correctly');
        console.log('  3. Multiple search providers selectable');
        console.log('  4. URL navigation works properly');
        
        return true;
    } else {
        console.log('❌ SOME TESTS FAILED! Please check the issues above.');
        return false;
    }
}

// Execute validation
runValidation().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
});
