<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phantom Browser Diagnostic Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #logOutput {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Phantom Browser Diagnostic Test</h1>
    <p>This page helps diagnose browsing issues by testing various browser components.</p>

    <div class="test-section">
        <h2>📊 System Information</h2>
        <div id="systemInfo"></div>
    </div>

    <div class="test-section">
        <h2>🔧 Component Tests</h2>
        <button class="test-button" onclick="testSearchEngine()">Test SearchEngine</button>
        <button class="test-button" onclick="testWebview()">Test Webview</button>
        <button class="test-button" onclick="testNavigation()">Test Navigation</button>
        <button class="test-button" onclick="testNetworking()">Test Networking</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📋 Diagnostic Actions</h2>
        <button class="test-button" onclick="showDiagnosticReport()">Show Diagnostic Report</button>
        <button class="test-button" onclick="exportLogs()">Export Logs</button>
        <button class="test-button" onclick="clearLogs()">Clear Logs</button>
        <button class="test-button" onclick="runFullTest()">Run Full Test Suite</button>
    </div>

    <div class="test-section">
        <h2>📝 Live Log Output</h2>
        <div id="logOutput"></div>
        <button class="test-button" onclick="clearLogOutput()">Clear Output</button>
    </div>

    <script>
        // Initialize diagnostic interface
        let logOutput = document.getElementById('logOutput');
        let testResults = document.getElementById('testResults');

        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const div = document.createElement('div');
            div.textContent = logEntry;
            div.className = type;
            logOutput.appendChild(div);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(logEntry);
        }

        function addResult(test, success, message) {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${test}:</strong> ${success ? '✅' : '❌'} ${message}`;
            testResults.appendChild(div);
        }

        // System Information
        function displaySystemInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Platform': navigator.platform,
                'Language': navigator.language,
                'Online': navigator.onLine,
                'Cookies Enabled': navigator.cookieEnabled,
                'Local Storage': typeof(Storage) !== "undefined",
                'Electron': typeof(window.require) !== "undefined"
            };

            let html = '<table style="width:100%">';
            for (const [key, value] of Object.entries(info)) {
                html += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
            }
            html += '</table>';
            
            document.getElementById('systemInfo').innerHTML = html;
        }

        // Test SearchEngine
        async function testSearchEngine() {
            log('Testing SearchEngine...', 'info');
            
            try {
                if (window.phantomBrowser && window.phantomBrowser.searchEngine) {
                    const engine = window.phantomBrowser.searchEngine;
                    
                    // Test provider count
                    const providers = engine.getProviders();
                    addResult('SearchEngine Providers', providers.length > 0, `Found ${providers.length} providers`);
                    
                    // Test default provider
                    const defaultProvider = engine.getDefaultProvider();
                    addResult('Default Provider', !!defaultProvider, defaultProvider ? defaultProvider.name : 'None');
                    
                    // Test search functionality
                    const searchUrl = engine.search('test query');
                    addResult('Search URL Generation', !!searchUrl, searchUrl);
                    
                    // Test input processing
                    const urlResult = engine.processInput('example.com');
                    const searchResult = engine.processInput('test search');
                    addResult('Input Processing', urlResult && searchResult, 'URL and search processing working');
                    
                    log('SearchEngine test completed', 'success');
                } else {
                    addResult('SearchEngine', false, 'SearchEngine not available');
                    log('SearchEngine not found', 'error');
                }
            } catch (error) {
                addResult('SearchEngine', false, error.message);
                log(`SearchEngine test failed: ${error.message}`, 'error');
            }
        }

        // Test Webview
        function testWebview() {
            log('Testing Webview...', 'info');
            
            try {
                const webview = document.getElementById('webview');
                if (webview) {
                    addResult('Webview Element', true, 'Webview found');
                    addResult('Webview Source', !!webview.src, webview.src || 'No source set');
                    addResult('Webview Loaded', webview.src !== 'about:blank', webview.src);
                    log('Webview test completed', 'success');
                } else {
                    addResult('Webview Element', false, 'Webview not found');
                    log('Webview element not found', 'error');
                }
            } catch (error) {
                addResult('Webview', false, error.message);
                log(`Webview test failed: ${error.message}`, 'error');
            }
        }

        // Test Navigation
        async function testNavigation() {
            log('Testing Navigation...', 'info');
            
            try {
                if (window.phantomAPI) {
                    // Test navigation API
                    const result = await window.phantomAPI.navigate('https://example.com');
                    addResult('Navigation API', result.success, result.success ? 'Navigation successful' : result.error);
                    
                    log('Navigation test completed', 'success');
                } else {
                    addResult('Navigation API', false, 'phantomAPI not available');
                    log('phantomAPI not found', 'error');
                }
            } catch (error) {
                addResult('Navigation', false, error.message);
                log(`Navigation test failed: ${error.message}`, 'error');
            }
        }

        // Test Networking
        function testNetworking() {
            log('Testing Networking...', 'info');
            
            try {
                // Test basic fetch
                fetch('https://httpbin.org/get')
                    .then(response => {
                        addResult('Network Fetch', response.ok, `Status: ${response.status}`);
                        log('Network test completed', 'success');
                    })
                    .catch(error => {
                        addResult('Network Fetch', false, error.message);
                        log(`Network test failed: ${error.message}`, 'error');
                    });
            } catch (error) {
                addResult('Networking', false, error.message);
                log(`Networking test failed: ${error.message}`, 'error');
            }
        }

        // Show diagnostic report
        async function showDiagnosticReport() {
            log('Generating diagnostic report...', 'info');
            
            try {
                if (window.phantomBrowser && window.phantomBrowser.showDiagnosticReport) {
                    await window.phantomBrowser.showDiagnosticReport();
                    log('Diagnostic report displayed', 'success');
                } else {
                    log('Diagnostic report not available', 'error');
                }
            } catch (error) {
                log(`Failed to show diagnostic report: ${error.message}`, 'error');
            }
        }

        // Export logs
        async function exportLogs() {
            log('Exporting logs...', 'info');
            
            try {
                if (window.phantomBrowser && window.phantomBrowser.exportDiagnosticReport) {
                    await window.phantomBrowser.exportDiagnosticReport();
                    log('Logs exported successfully', 'success');
                } else {
                    log('Log export not available', 'error');
                }
            } catch (error) {
                log(`Failed to export logs: ${error.message}`, 'error');
            }
        }

        // Clear logs
        function clearLogs() {
            localStorage.removeItem('phantom-browser-logs');
            log('Local logs cleared', 'success');
        }

        // Clear log output
        function clearLogOutput() {
            logOutput.innerHTML = '';
        }

        // Run full test suite
        async function runFullTest() {
            log('Starting full test suite...', 'info');
            testResults.innerHTML = '';
            
            await testSearchEngine();
            testWebview();
            await testNavigation();
            testNetworking();
            
            log('Full test suite completed', 'success');
        }

        // Initialize on load
        window.addEventListener('load', () => {
            displaySystemInfo();
            log('Diagnostic interface loaded', 'success');
        });
    </script>
</body>
</html>
