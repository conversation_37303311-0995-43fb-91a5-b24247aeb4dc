# 🧪 Production Build Manual Test Checklist

## ⚡ Quick Test (2 minutes)

### ✅ Application Startup
- [ ] Application starts within 10 seconds
- [ ] No red error messages appear
- [ ] UI loads completely (address bar, webview, buttons visible)
- [ ] No console errors (press F12 to check)

### ✅ Search Engine Functionality  
- [ ] Type `test search` in address bar → Press Enter
- [ ] Webview loads search results page
- [ ] Type `google.com` in address bar → Press Enter  
- [ ] Webview loads Google homepage

### ✅ Network Configuration
- [ ] Click privacy/settings button (⚙)
- [ ] Privacy panel opens
- [ ] Click "Configure Network" or similar button
- [ ] Network dialog opens WITHOUT "window.phantom is not a function" error

### ✅ Search Provider Interface
- [ ] In privacy panel, find "Search Engine" section
- [ ] Search provider dropdown is visible
- [ ] Shows current provider (e.g., "DuckDuckGo")
- [ ] Provider details display below dropdown

## 🔍 Detailed Test (10 minutes)

### Search Functionality Tests
| Test | Input | Expected Result | ✅/❌ |
|------|-------|----------------|-------|
| Search Query | `phantom browser` | Shows search results | [ ] |
| URL Direct | `https://example.com` | Loads example.com | [ ] |
| Domain Only | `github.com` | Loads GitHub with https:// | [ ] |
| Search with Spaces | `privacy tools test` | Shows search results | [ ] |
| Special Characters | `test & search` | Properly encoded search | [ ] |

### Provider Selection Tests
| Test | Action | Expected Result | ✅/❌ |
|------|--------|----------------|-------|
| Provider Dropdown | Open privacy panel | Dropdown visible | [ ] |
| Provider Options | Click dropdown | Multiple providers listed | [ ] |
| Provider Switch | Select different provider | Interface updates | [ ] |
| Provider Info | View provider details | Shows privacy rating & features | [ ] |

### Network Configuration Tests
| Test | Action | Expected Result | ✅/❌ |
|------|--------|----------------|-------|
| Open Network Dialog | Click "Configure Network" | Dialog opens without errors | [ ] |
| Switch to Proxy | Select proxy mode | No errors, settings apply | [ ] |
| Switch to DoH | Select DoH mode | No errors, settings apply | [ ] |
| Switch to Direct | Select direct mode | No errors, settings apply | [ ] |

### UI Component Tests
| Component | Location | Expected | ✅/❌ |
|-----------|----------|----------|-------|
| Address Bar | Top toolbar | Accepts input, responds to Enter | [ ] |
| Search Section | Privacy panel | "Search Engine" section visible | [ ] |
| Provider Dropdown | Search section | Shows current provider | [ ] |
| Provider Details | Below dropdown | Shows provider info | [ ] |
| Search Toggles | Search section | Suggestions/History toggles | [ ] |

## 🧪 Console Testing

### Open Developer Console
1. Press **F12** or **Ctrl+Shift+I**
2. Go to **Console** tab
3. Paste the test script from `PRODUCTION_BUILD_TEST_SCRIPT.js`
4. Press **Enter** to run

### Expected Console Messages
- [ ] "Successfully imported SearchEngine and SearchUI modules" OR
- [ ] "Using fallback SearchUI implementation"
- [ ] "Search engine initialized successfully"
- [ ] No red error messages

### Test Script Results
- [ ] Application Startup: PASS
- [ ] Search Engine: PASS  
- [ ] Network Configuration: PASS
- [ ] UI Components: PASS
- [ ] Navigation: PASS
- [ ] Overall Score: ≥80%

## 🚨 Known Issues to Check

### ❌ Critical Issues (Must Fix)
- [ ] "window.phantom is not a function" error
- [ ] Search queries not working
- [ ] Application won't start
- [ ] Network configuration dialog won't open

### ⚠️ Warning Issues (Should Fix)
- [ ] Search provider dropdown missing
- [ ] Provider switching not working
- [ ] Console shows fallback mode
- [ ] UI elements missing

### ✅ Acceptable Issues (Minor)
- [ ] Search suggestions not available (fallback mode)
- [ ] Some advanced features limited
- [ ] Minor UI styling issues

## 📊 Test Results Template

```
PRODUCTION BUILD TEST RESULTS
=============================
Date: ___________
Tester: ___________
Build: phantom-browser-working.exe

QUICK TESTS:
[ ] Application starts < 10 seconds
[ ] Search queries work (test search → results)
[ ] URL navigation works (google.com → Google)
[ ] Network config opens without errors
[ ] Search provider interface visible

DETAILED TESTS:
[ ] All search functionality tests pass
[ ] All provider selection tests pass  
[ ] All network configuration tests pass
[ ] All UI component tests pass

CONSOLE TESTS:
[ ] Test script runs without errors
[ ] Module imports successful
[ ] Overall score ≥80%
[ ] No red error messages

CRITICAL FIXES VERIFIED:
[ ] Network API "window.phantom" error FIXED
[ ] Search engine functionality WORKING
[ ] Provider selection interface VISIBLE
[ ] All navigation features FUNCTIONAL

OVERALL RATING: ___/10
BUILD STATUS: [ ] READY [ ] NEEDS FIXES [ ] MAJOR ISSUES

NOTES:
_________________________________
_________________________________
_________________________________
```

## 🎯 Success Criteria

### ✅ Minimum Requirements (Must Pass)
- ✅ Application starts without errors
- ✅ Search queries return results
- ✅ URL navigation works
- ✅ Network configuration opens without "window.phantom" error
- ✅ Search provider interface is visible

### 🌟 Full Functionality (Ideal)
- ✅ All minimum requirements
- ✅ Provider switching works
- ✅ Provider information displays
- ✅ Search suggestions available (if not in fallback mode)
- ✅ All UI components functional

### ⚠️ Fallback Acceptable
- ✅ Basic search and URL navigation work
- ✅ Provider selection available
- ✅ Console shows "Using fallback SearchUI implementation"
- ⚠️ Advanced features may be limited

## 🔧 Troubleshooting

### If Tests Fail:
1. **Check Console**: Look for specific error messages
2. **Restart Application**: Close and reopen the browser
3. **Clear Cache**: Try refreshing the page
4. **Check Files**: Verify all files were copied correctly

### If Search Engine Not Working:
1. Check if SearchEngine modules are in `dist/search/`
2. Verify renderer.js has import logic
3. Look for module import errors in console
4. Test fallback functionality

### If Network Config Fails:
1. Check if preload.js has network methods
2. Verify main.js has IPC handlers
3. Look for "window.phantom is not a function" error
4. Check if phantomAPI is available

---

**Expected Result:** All tests should pass with ≥80% success rate, indicating the production build is ready for deployment.
