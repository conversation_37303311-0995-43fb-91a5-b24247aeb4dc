import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';

export interface LogEntry {
    timestamp: string;
    level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
    category: 'STARTUP' | 'SEARCH' | 'NAVIGATION' | 'WEBVIEW' | 'NETWORK' | 'ERROR';
    message: string;
    data?: any;
}

export class DiagnosticLogger {
    private logFile: string;
    private logStream: fs.WriteStream | null = null;
    private logBuffer: LogEntry[] = [];
    private maxBufferSize = 100;

    constructor() {
        const userDataPath = app.getPath('userData');
        const logsDir = path.join(userDataPath, 'logs');
        
        // Ensure logs directory exists
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }

        // Create timestamped log file
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        this.logFile = path.join(logsDir, `phantom-browser-${timestamp}.log`);
        
        this.initializeLogFile();
    }

    private initializeLogFile(): void {
        try {
            this.logStream = fs.createWriteStream(this.logFile, { flags: 'a' });
            this.log('INFO', 'STARTUP', 'Diagnostic logging initialized', { logFile: this.logFile });
        } catch (error) {
            console.error('Failed to initialize log file:', error);
        }
    }

    public log(level: LogEntry['level'], category: LogEntry['category'], message: string, data?: any): void {
        const entry: LogEntry = {
            timestamp: new Date().toISOString(),
            level,
            category,
            message,
            data
        };

        // Add to buffer
        this.logBuffer.push(entry);
        if (this.logBuffer.length > this.maxBufferSize) {
            this.logBuffer.shift();
        }

        // Write to console
        const consoleMessage = `[${entry.timestamp}] ${entry.level} [${entry.category}] ${entry.message}`;
        console.log(consoleMessage, data ? data : '');

        // Write to file
        this.writeToFile(entry);
    }

    private writeToFile(entry: LogEntry): void {
        if (!this.logStream) return;

        try {
            const logLine = `[${entry.timestamp}] ${entry.level} [${entry.category}] ${entry.message}`;
            const dataLine = entry.data ? `\n  DATA: ${JSON.stringify(entry.data, null, 2)}` : '';
            this.logStream.write(`${logLine}${dataLine}\n`);
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }

    // Convenience methods for different log levels
    public debug(category: LogEntry['category'], message: string, data?: any): void {
        this.log('DEBUG', category, message, data);
    }

    public info(category: LogEntry['category'], message: string, data?: any): void {
        this.log('INFO', category, message, data);
    }

    public warn(category: LogEntry['category'], message: string, data?: any): void {
        this.log('WARN', category, message, data);
    }

    public error(category: LogEntry['category'], message: string, data?: any): void {
        this.log('ERROR', category, message, data);
    }

    // Specific logging methods for different browser events
    public logStartup(step: string, success: boolean, details?: any): void {
        this.log(success ? 'INFO' : 'ERROR', 'STARTUP', `Startup step: ${step}`, { success, ...details });
    }

    public logSearchEngine(action: string, success: boolean, details?: any): void {
        this.log(success ? 'INFO' : 'ERROR', 'SEARCH', `SearchEngine: ${action}`, { success, ...details });
    }

    public logNavigation(url: string, method: string, success: boolean, details?: any): void {
        this.log(success ? 'INFO' : 'ERROR', 'NAVIGATION', `Navigation to ${url} via ${method}`, { success, url, method, ...details });
    }

    public logWebview(event: string, url?: string, details?: any): void {
        this.log('INFO', 'WEBVIEW', `Webview event: ${event}`, { url, ...details });
    }

    public logWebviewError(error: string, url?: string, details?: any): void {
        this.log('ERROR', 'WEBVIEW', `Webview error: ${error}`, { url, ...details });
    }

    public logNetwork(method: string, url: string, status?: number, details?: any): void {
        const level = status && status >= 400 ? 'ERROR' : 'INFO';
        this.log(level, 'NETWORK', `${method} ${url}`, { status, ...details });
    }

    public getRecentLogs(count: number = 50): LogEntry[] {
        return this.logBuffer.slice(-count);
    }

    public getLogFilePath(): string {
        return this.logFile;
    }

    public close(): void {
        if (this.logStream) {
            this.logStream.end();
            this.logStream = null;
        }
    }
}

// Global logger instance
export const logger = new DiagnosticLogger();
