// Test script for Search Engine functionality
// This script can be run in the browser console to test search functionality

console.log('🔍 Testing Search Engine Functionality');
console.log('=====================================');

// Test 1: Check if SearchEngine and SearchUI modules are loaded
function testModuleImports() {
    console.log('\n📦 Test 1: Module Import Status');
    console.log('--------------------------------');
    
    try {
        // Check if compiled modules are available
        const searchEngineModule = window.require('../dist/search/SearchEngine.js');
        const searchUIModule = window.require('../dist/search/SearchUI.js');
        
        console.log('✅ SearchEngine module imported successfully');
        console.log('✅ SearchUI module imported successfully');
        
        return {
            searchEngine: searchEngineModule.SearchEngine,
            searchUI: searchUIModule.SearchUI
        };
    } catch (error) {
        console.log('❌ Failed to import modules:', error.message);
        console.log('🔄 Will use fallback implementation');
        return null;
    }
}

// Test 2: Check PhantomBrowserUI initialization
function testUIInitialization() {
    console.log('\n🖥️ Test 2: UI Initialization');
    console.log('-----------------------------');
    
    if (window.phantomBrowserUI) {
        console.log('✅ PhantomBrowserUI is available');
        
        if (window.phantomBrowserUI.searchEngine) {
            console.log('✅ SearchEngine is initialized');
            
            const providers = window.phantomBrowserUI.searchEngine.getProviders();
            console.log(`✅ Found ${providers.length} search providers:`, providers.map(p => p.name));
            
            const defaultProvider = window.phantomBrowserUI.searchEngine.getDefaultProvider();
            console.log(`✅ Default provider: ${defaultProvider.name}`);
        } else {
            console.log('❌ SearchEngine is not initialized');
        }
        
        if (window.phantomBrowserUI.searchUI) {
            console.log('✅ SearchUI is initialized');
        } else {
            console.log('⚠️ SearchUI not available (using fallback)');
        }
    } else {
        console.log('❌ PhantomBrowserUI is not available');
    }
}

// Test 3: Test search functionality
function testSearchFunctionality() {
    console.log('\n🔍 Test 3: Search Functionality');
    console.log('-------------------------------');
    
    if (!window.phantomBrowserUI || !window.phantomBrowserUI.searchEngine) {
        console.log('❌ Cannot test - SearchEngine not available');
        return;
    }
    
    const searchEngine = window.phantomBrowserUI.searchEngine;
    
    // Test URL detection
    const urlTests = [
        'https://google.com',
        'example.com',
        'github.com'
    ];
    
    console.log('🌐 Testing URL detection:');
    urlTests.forEach(url => {
        const result = searchEngine.processInput(url);
        console.log(`  "${url}" → ${result.type}: ${result.value}`);
    });
    
    // Test search queries
    const searchTests = [
        'test search',
        'phantom browser',
        'privacy tools'
    ];
    
    console.log('\n🔍 Testing search queries:');
    searchTests.forEach(query => {
        const result = searchEngine.processInput(query);
        console.log(`  "${query}" → ${result.type}: ${result.value}`);
    });
}

// Test 4: Test UI elements
function testUIElements() {
    console.log('\n🎨 Test 4: UI Elements');
    console.log('----------------------');
    
    // Check address bar
    const addressBar = document.getElementById('addressBar');
    if (addressBar) {
        console.log('✅ Address bar found');
    } else {
        console.log('❌ Address bar not found');
    }
    
    // Check search provider selector
    const providerSelect = document.getElementById('searchProvider');
    if (providerSelect) {
        console.log('✅ Search provider selector found');
        console.log(`   Options: ${providerSelect.options.length}`);
        console.log(`   Selected: ${providerSelect.value}`);
    } else {
        console.log('❌ Search provider selector not found');
    }
    
    // Check search suggestions container
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        console.log('✅ Search suggestions container found');
        console.log(`   Classes: ${suggestionsContainer.className}`);
    } else {
        console.log('❌ Search suggestions container not found');
    }
    
    // Check provider details
    const providerDetails = document.getElementById('providerDetails');
    if (providerDetails) {
        console.log('✅ Provider details section found');
        console.log(`   Content length: ${providerDetails.innerHTML.length} chars`);
    } else {
        console.log('❌ Provider details section not found');
    }
}

// Test 5: Test search suggestions
async function testSearchSuggestions() {
    console.log('\n💡 Test 5: Search Suggestions');
    console.log('-----------------------------');
    
    if (!window.phantomBrowserUI || !window.phantomBrowserUI.searchEngine) {
        console.log('❌ Cannot test - SearchEngine not available');
        return;
    }
    
    const searchEngine = window.phantomBrowserUI.searchEngine;
    
    try {
        const suggestions = await searchEngine.getSuggestions('test');
        console.log(`✅ Got ${suggestions.length} suggestions for "test"`);
        suggestions.forEach((suggestion, index) => {
            console.log(`  ${index + 1}. ${suggestion.query} (${suggestion.type})`);
        });
    } catch (error) {
        console.log('❌ Failed to get suggestions:', error.message);
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Starting Search Engine Tests...\n');
    
    const modules = testModuleImports();
    testUIInitialization();
    testSearchFunctionality();
    testUIElements();
    await testSearchSuggestions();
    
    console.log('\n✨ Tests completed!');
    console.log('\n📋 Manual Testing Instructions:');
    console.log('1. Type a URL in the address bar (e.g., "google.com") and press Enter');
    console.log('2. Type a search query (e.g., "test search") and press Enter');
    console.log('3. Check if the search provider dropdown is visible in the privacy panel');
    console.log('4. Try changing the search provider and see if it updates');
    console.log('5. Look for any error messages in the console');
}

// Auto-run tests when script is loaded
runAllTests();

// Export test functions for manual use
window.searchEngineTests = {
    testModuleImports,
    testUIInitialization,
    testSearchFunctionality,
    testUIElements,
    testSearchSuggestions,
    runAllTests
};

console.log('\n🔧 Available test functions:');
console.log('- searchEngineTests.runAllTests()');
console.log('- searchEngineTests.testModuleImports()');
console.log('- searchEngineTests.testUIInitialization()');
console.log('- searchEngineTests.testSearchFunctionality()');
console.log('- searchEngineTests.testUIElements()');
console.log('- searchEngineTests.testSearchSuggestions()');
