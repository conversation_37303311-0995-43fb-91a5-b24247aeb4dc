export interface SearchProvider {
    id: string;
    name: string;
    baseUrl: string;
    searchUrl: string;
    suggestionsUrl?: string;
    icon?: string;
    privacyRating: number; // 1-10, 10 being most private
    features: string[];
    description: string;
}

export interface SearchSuggestion {
    query: string;
    type: 'suggestion' | 'history' | 'bookmark';
    url?: string;
    title?: string;
}

export interface SearchSettings {
    defaultProvider: string;
    enableSuggestions: boolean;
    enableHistory: boolean;
    maxSuggestions: number;
    privacyMode: 'strict' | 'balanced' | 'standard';
    customProviders: SearchProvider[];
}

import { SettingsStore } from '../utils/SettingsStore';

export class SearchEngine {
    private providers: Map<string, SearchProvider> = new Map();
    private settings!: SearchSettings;
    private searchHistory: Array<{ query: string; timestamp: number; provider: string }> = [];
    private maxHistorySize = 1000;
    private settingsStore: SettingsStore;

    constructor() {
        this.settingsStore = SettingsStore.getInstance();
        this.initializeDefaultProviders();
        this.loadSettings();
    }

    private initializeDefaultProviders(): void {
        const defaultProviders: SearchProvider[] = [
            {
                id: 'duckduckgo',
                name: 'DuckDuckGo',
                baseUrl: 'https://duckduckgo.com',
                searchUrl: 'https://duckduckgo.com/?q={query}&t=phantom',
                suggestionsUrl: 'https://duckduckgo.com/ac/?q={query}&type=list',
                icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40NzcgMiAyIDYuNDc3IDIgMTJTNi40NzcgMjIgMTIgMjJTMjIgMTcuNTIzIDIyIDEyUzE3LjUyMyAyIDEyIDJaIiBmaWxsPSIjREU1ODMzIi8+CjxwYXRoIGQ9Ik0xMiA2QzE1LjMxNCA2IDE4IDguNjg2IDE4IDEyUzE1LjMxNCAxOCAxMiAxOFM2IDE1LjMxNCA2IDEyUzguNjg2IDYgMTIgNloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=',
                privacyRating: 10,
                features: ['No tracking', 'No ads', 'Tor support', 'Instant answers'],
                description: 'Privacy-focused search engine with no tracking'
            },
            {
                id: 'startpage',
                name: 'Startpage',
                baseUrl: 'https://www.startpage.com',
                searchUrl: 'https://www.startpage.com/sp/search?query={query}&cat=web&pl=phantom',
                suggestionsUrl: 'https://www.startpage.com/cgi-bin/csuggest?query={query}&limit=10',
                privacyRating: 9,
                features: ['Google results', 'No tracking', 'Anonymous view', 'EU based'],
                description: 'Private search with Google results, no tracking'
            },
            {
                id: 'searx',
                name: 'SearX',
                baseUrl: 'https://searx.org',
                searchUrl: 'https://searx.org/search?q={query}&categories=general',
                privacyRating: 10,
                features: ['Open source', 'No tracking', 'Aggregated results', 'Self-hostable'],
                description: 'Open source metasearch engine'
            },
            {
                id: 'brave',
                name: 'Brave Search',
                baseUrl: 'https://search.brave.com',
                searchUrl: 'https://search.brave.com/search?q={query}&source=phantom',
                suggestionsUrl: 'https://search.brave.com/api/suggest?q={query}',
                privacyRating: 8,
                features: ['Independent index', 'No tracking', 'Ad-free', 'Fast results'],
                description: 'Independent search engine by Brave'
            },
            {
                id: 'yandex',
                name: 'Yandex',
                baseUrl: 'https://yandex.com',
                searchUrl: 'https://yandex.com/search/?text={query}&lr=0',
                suggestionsUrl: 'https://suggest.yandex.com/suggest-ff.cgi?part={query}',
                privacyRating: 4,
                features: ['Russian focus', 'Image search', 'Maps integration', 'Translation'],
                description: 'Russian search engine with global reach'
            },
            {
                id: 'bing',
                name: 'Bing',
                baseUrl: 'https://www.bing.com',
                searchUrl: 'https://www.bing.com/search?q={query}&form=phantom',
                suggestionsUrl: 'https://www.bing.com/AS/Suggestions?pt=page.home&mkt=en-us&qry={query}',
                privacyRating: 3,
                features: ['Microsoft integration', 'Visual search', 'News integration', 'Rewards'],
                description: 'Microsoft search engine with visual features'
            }
        ];

        defaultProviders.forEach(provider => {
            this.providers.set(provider.id, provider);
        });
    }

    private getDefaultSettings(): SearchSettings {
        return {
            defaultProvider: 'duckduckgo',
            enableSuggestions: true,
            enableHistory: true,
            maxSuggestions: 8,
            privacyMode: 'balanced',
            customProviders: []
        };
    }

    private loadSettings(): void {
        try {
            this.settings = this.settingsStore.get('search');
            console.log('Search engine settings loaded from storage');
        } catch (error) {
            console.error('Failed to load search settings:', error);
            // Use default settings if loading fails
            this.settings = this.getDefaultSettings();
        }
    }

    public getProviders(): SearchProvider[] {
        return Array.from(this.providers.values());
    }

    public getProvider(id: string): SearchProvider | undefined {
        return this.providers.get(id);
    }

    public getDefaultProvider(): SearchProvider {
        return this.providers.get(this.settings.defaultProvider) || this.providers.get('duckduckgo')!;
    }

    public getDefaultHomepage(): string {
        const defaultProvider = this.getDefaultProvider();
        return defaultProvider.baseUrl;
    }

    public setDefaultProvider(providerId: string): void {
        if (this.providers.has(providerId)) {
            this.settings.defaultProvider = providerId;
            this.saveSettings();
        }
    }

    public search(query: string, providerId?: string): string {
        const provider = providerId ? this.providers.get(providerId) : this.getDefaultProvider();
        if (!provider) {
            throw new Error('Search provider not found');
        }

        // Add to search history
        this.addToHistory(query, provider.id);

        // Replace query placeholder in URL
        const searchUrl = provider.searchUrl.replace('{query}', encodeURIComponent(query));
        
        console.log(`Searching "${query}" with ${provider.name}: ${searchUrl}`);
        return searchUrl;
    }

    public async getSuggestions(query: string, providerId?: string): Promise<SearchSuggestion[]> {
        if (!this.settings.enableSuggestions || query.length < 2) {
            return [];
        }

        const suggestions: SearchSuggestion[] = [];

        // Add history suggestions
        if (this.settings.enableHistory) {
            const historySuggestions = this.getHistorySuggestions(query);
            suggestions.push(...historySuggestions);
        }

        // Add provider suggestions
        const provider = providerId ? this.providers.get(providerId) : this.getDefaultProvider();
        if (provider?.suggestionsUrl) {
            try {
                const providerSuggestions = await this.fetchProviderSuggestions(query, provider);
                suggestions.push(...providerSuggestions);
            } catch (error) {
                console.warn('Failed to fetch suggestions:', error);
            }
        }

        // Limit results
        return suggestions.slice(0, this.settings.maxSuggestions);
    }

    private async fetchProviderSuggestions(query: string, provider: SearchProvider): Promise<SearchSuggestion[]> {
        // This would make actual HTTP requests in a real implementation
        // For now, return mock suggestions
        const mockSuggestions = [
            `${query} tutorial`,
            `${query} guide`,
            `${query} examples`,
            `${query} documentation`
        ];

        return mockSuggestions.map(suggestion => ({
            query: suggestion,
            type: 'suggestion' as const
        }));
    }

    private getHistorySuggestions(query: string): SearchSuggestion[] {
        const lowerQuery = query.toLowerCase();
        return this.searchHistory
            .filter(item => item.query.toLowerCase().includes(lowerQuery))
            .slice(0, 3)
            .map(item => ({
                query: item.query,
                type: 'history' as const
            }));
    }

    private addToHistory(query: string, provider: string): void {
        // Remove existing entry if present
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        
        // Add new entry at the beginning
        this.searchHistory.unshift({
            query,
            timestamp: Date.now(),
            provider
        });

        // Limit history size
        if (this.searchHistory.length > this.maxHistorySize) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
        }
    }

    public clearHistory(): void {
        this.searchHistory = [];
        console.log('Search history cleared');
    }

    public getSettings(): SearchSettings {
        return { ...this.settings };
    }

    public updateSettings(newSettings: Partial<SearchSettings>): void {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
    }

    private saveSettings(): void {
        try {
            this.settingsStore.updateSearchSettings(this.settings);
            console.log('Search engine settings saved to storage');
        } catch (error) {
            console.error('Failed to save search settings:', error);
            throw new Error(`Failed to save search settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    public addCustomProvider(provider: SearchProvider): void {
        this.providers.set(provider.id, provider);
        this.settings.customProviders.push(provider);
        this.saveSettings();
    }

    public removeCustomProvider(providerId: string): void {
        this.providers.delete(providerId);
        this.settings.customProviders = this.settings.customProviders.filter(p => p.id !== providerId);
        this.saveSettings();
    }

    public getSearchHistory(): Array<{ query: string; timestamp: number; provider: string }> {
        return [...this.searchHistory];
    }

    public isValidUrl(input: string): boolean {
        try {
            new URL(input);
            return true;
        } catch {
            // Check for domain-like patterns
            const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
            return domainPattern.test(input) || input.includes('.');
        }
    }

    public processInput(input: string): { type: 'url' | 'search'; value: string } {
        const trimmedInput = input.trim();
        
        if (this.isValidUrl(trimmedInput)) {
            // Add protocol if missing
            const url = trimmedInput.startsWith('http') ? trimmedInput : `https://${trimmedInput}`;
            return { type: 'url', value: url };
        } else {
            // Treat as search query
            const searchUrl = this.search(trimmedInput);
            return { type: 'search', value: searchUrl };
        }
    }
}
